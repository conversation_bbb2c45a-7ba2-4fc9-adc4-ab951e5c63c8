# Product Requirements Document (PRD)

## Проект: G-MCP Server

### Краткое описание

G-MCP — это сервер, превращающий намерения пользователя в код и решения с помощью ChatGPT (через fastmcp). Проект реализует магию суперпозиции: любой запрос пользователя (prompt) или структурированное намерение (tool) обрабатывается как диалог с «вселенной», а результат возвращается мгновенно и прозрачно.

---

## 1. Цели и задачи

- **Минимальный барьер входа:** Не требуются внешние API-ключи, всё работает через встроенный ChatGPT (ctx.sample()).
- **Гибкость:** Любой вопрос или задача пользователя обрабатывается как prompt.
- **Расширяемость:** Возможность добавлять новые инструменты (tools) для генерации кода, функций и решений по описанию.
- **Прозрачность:** Вся логика и ответы логируются через ctx.info().
- **Философия:** Каждый запрос — это цикл наблюдения, результат фиксируется и используется для эволюции системы.

---

## 2. Основные сценарии использования

### 2.1. Свободный prompt

- Пользователь задаёт любой вопрос или формулирует задачу.
- MCP-сервер отправляет запрос напрямую в ChatGPT через ctx.sample().
- Ответ возвращается пользователю, логируется в ctx.info().

### 2.2. Генерация Python-функций по описанию

- Пользователь описывает желаемую функцию (например: «Функция для вычисления гравитационного притяжения между двумя массами»).
- MCP-сервер через ctx.sample() генерирует код Python-функции с type hints и докстрингом.
- Код возвращается пользователю, логируется.

---

## 3. Ключевые требования

- **Язык:** Python 3.10+
- **Библиотека:** fastmcp
- **Файл запуска:** gmcp.py
- **Вызов:** `fastmcp dev gmcp.py`
- **Без внешних ключей:** Не требуются OpenAI API-ключи.
- **Асинхронность:** Все функции асинхронные (async def ...).
- **Логирование:** Использовать ctx.info для всех этапов обработки.
- **Гибкая обработка ответов:** Поддержка как строк, так и объектов с атрибутом text.

---

## 4. Примеры использования

### 4.1. Prompt

```
Вопрос: "Напиши стратегию победы цивилизации над хаосом."
Ответ: (творческий текст от ChatGPT)
```

### 4.2. Tool

```
Описание: "Функция для вычисления гравитационного притяжения между двумя массами."
Ответ: (Python-код с type hints и докстрингом)
```

---

## 5. Философия и принципы

- Каждый запрос — это наблюдение, результат фиксируется.
- G-MCP — посредник между намерением и кодом.
- Костыли — двигатель эволюции: не бойся экспериментировать и логировать всё.

---

## 6. Возможности для расширения

- Добавление новых tools для генерации кода по шаблонам.
- Интеграция с внешними системами через ctx.sample().
- Расширение логики логирования и анализа запросов.

---

## 7. Ссылки и документация

- [GMCP.md](./GMCP.md) — философия и инструкция пользователя
- [gmcp.py](./gmcp.py) — основной серверный код
