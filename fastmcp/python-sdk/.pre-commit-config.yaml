fail_fast: true

repos:
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, json5]

  - repo: local
    hooks:
      - id: ruff-format
        name: Ruff Format
        entry: uv run ruff
        args: [format]
        language: system
        types: [python]
        pass_filenames: false
      - id: ruff
        name: Ruff
        entry: uv run ruff
        args: ["check", "--fix", "--exit-non-zero-on-fix"]
        types: [python]
        language: system
        pass_filenames: false
      - id: pyright
        name: pyright
        entry: uv run pyright
        args: [src]
        language: system
        types: [python]
        pass_filenames: false
      - id: uv-lock-check
        name: Check uv.lock is up to date
        entry: uv lock --check
        language: system
        files: ^(pyproject\.toml|uv\.lock)$
        pass_filenames: false
