from fastmcp import FastMCP, Context

# Этот MCP-сервер теперь полностью использует ChatGPT через ctx.sample(),
# никакие внешние API-ключи или импорты не требуются.

mcp = FastMCP("G-MCP Server")

@mcp.prompt()
async def ask_gpt(prompt: str, ctx: Context) -> str:
    """
    Отправляет запрос напрямую через ChatGPT с использованием ctx.sample().

    Args:
        prompt (str): Вопрос или сообщение.
        ctx (Context): Контекст вызова MCP.

    Returns:
        str: Ответ от ChatGPT.
    """
    await ctx.info(f"Обрабатываю через ChatGPT напрямую: {prompt}")
    reply = await ctx.sample(prompt)
    await ctx.info(f"Ответ готов.")
    if isinstance(reply, str):
        return reply
    text = getattr(reply, "text", None)
    if text is not None:
        return text
    return f"[Ошибка: неподдерживаемый тип ответа: {type(reply)}]"


@mcp.tool()
async def generate_python_function(description: str, ctx: Context) -> str:
    """
    Генерирует Python-функцию по описанию через ctx.sample().

    Args:
        description (str): Описание желаемой функции.
        ctx (Context): Контекст вызова MCP.

    Returns:
        str: Исходный код Python-функции.
    """
    await ctx.info(f"Генерирую Python-функцию по описанию: {description}")
    full_prompt = (
        f"Напиши Python-функцию с полными type hints и докстрингом. "
        f"Описание функции: {description}. Только код без пояснений."
    )
    reply = await ctx.sample(full_prompt)
    await ctx.info("Функция сгенерирована")
    if isinstance(reply, str):
        return reply
    text = getattr(reply, "text", None)
    if text is not None:
        return text
    return f"[Ошибка: неподдерживаемый тип ответа: {type(reply)}]"


if __name__ == "__main__":
    mcp.run()