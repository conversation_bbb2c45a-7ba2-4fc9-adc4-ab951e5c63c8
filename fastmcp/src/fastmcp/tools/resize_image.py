from fastmcp.tools import tool
from fastmcp.utilities.types import Image
from PIL import Image as PILImage
from pathlib import Path
from typing import Optional

@tool()
def resize_image(
    input_path: Path,
    width: int = 128,
    height: int = 128,
    output_path: Optional[str] = None
) -> Image:
    """
    Уменьшает изображение до указанных размеров и возвращает MCP Image.

    Args:
        input_path (Path): Путь к исходному изображению.
        width (int): Новая ширина (по умолчанию 128).
        height (int): Новая высота (по умолчанию 128).
        output_path (str, optional): Путь для сохранения уменьшенного изображения. Если не указан, добавляется _small к имени файла.

    Returns:
        Image: MCP Image-объект с уменьшенным изображением.
    """
    if output_path is None:
        output_path = str(input_path.with_name(input_path.stem + f'_small{input_path.suffix}'))
    img = PILImage.open(input_path)
    img_small = img.resize((width, height), PILImage.LANCZOS)
    img_small.save(output_path)
    return Image(path=output_path) 