import pytest
from mcp.types import PromptReference, TextContent

from fastmcp import Client
from gmcp import mcp


@pytest.mark.asyncio
async def test_ask_gpt_returns_text():
    async with <PERSON><PERSON>(mcp) as client:
        prompts = await client.list_prompts()
        prompt_names = [p.name for p in prompts]
        assert "ask_gpt" in prompt_names, f"Prompt 'ask_gpt' not found, available: {prompt_names}"
        ref = PromptReference(type="ref/prompt", name="ask_gpt")
        try:
            result = await client.complete(ref, {"name": "prompt", "value": "Сколько будет 2+2?"})
        except Exception as e:
            pytest.skip(f"Prompt completion not supported: {e}")
        assert hasattr(result, "values")
        text = result.values[0] if result.values else ""
        assert isinstance(text, str)
        assert "4" in text or "четыр" in text.lower()

@pytest.mark.asyncio
async def test_generate_python_function_returns_code():
    async with <PERSON><PERSON>(mcp) as client:
        description = "Функция, возвращающая сумму двух чисел a и b."
        try:
            result = await client.call_tool("generate_python_function", {"description": description})
        except Exception as e:
            if "Sampling not supported" in str(e):
                pytest.skip("Sampling not supported in this environment")
            raise
        assert isinstance(result, list)
        assert len(result) > 0
        content = next((c for c in result if isinstance(c, TextContent)), None)
        assert content is not None, "Tool did not return TextContent"
        assert "def" in content.text
        assert "return" in content.text
        assert "a" in content.text and "b" in content.text 