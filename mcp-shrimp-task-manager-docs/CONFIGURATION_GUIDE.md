# ⚙️ Shrimp Task Manager - Руководство по конфигурации

## 🎯 Обзор конфигурации

Shrimp Task Manager предоставляет гибкие возможности конфигурации через переменные окружения и файлы конфигурации. Это позволяет адаптировать поведение системы под конкретные потребности проекта.

## 📁 Структура конфигурации

### Основные переменные окружения

| Переменная | Тип | Обязательная | Описание |
|------------|-----|--------------|----------|
| `DATA_DIR` | string | ✅ | Абсолютный путь к директории данных проекта |
| `TEMPLATES_USE` | string | ❌ | Язык шаблонов: `en`, `zh`, или кастомная директория |
| `ENABLE_GUI` | boolean | ❌ | Включение графического интерфейса (по умолчанию: `false`) |

### Переменные кастомизации промптов

| Переменная | Описание |
|------------|----------|
| `MCP_PROMPT_PLAN_TASK` | Промпт для планирования задач |
| `MCP_PROMPT_PLAN_TASK_APPEND` | Дополнение к промпту планирования |
| `MCP_PROMPT_ANALYZE_TASK` | Промпт для анализа задач |
| `MCP_PROMPT_EXECUTE_TASK` | Промпт для выполнения задач |
| `MCP_PROMPT_EXECUTE_TASK_APPEND` | Дополнение к промпту выполнения |
| `MCP_PROMPT_VERIFY_TASK` | Промпт для проверки задач |
| `MCP_PROMPT_LIST_TASKS` | Промпт для отображения списка задач |

## 🔧 Примеры конфигурации

### 1. Базовая конфигурация для Claude Desktop

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["/path/to/mcp-shrimp-task-manager/dist/index.js"],
      "env": {
        "DATA_DIR": "/Users/<USER>/projects/my-project/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

### 2. Конфигурация с кастомными промптами

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["/path/to/mcp-shrimp-task-manager/dist/index.js"],
      "env": {
        "DATA_DIR": "/Users/<USER>/projects/my-project/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false",
        "MCP_PROMPT_PLAN_TASK": "## Custom Task Planning\n\nPlease plan the task based on the following information:\n\n{description}\n\nRequirements: {requirements}\n",
        "MCP_PROMPT_EXECUTE_TASK_APPEND": "\n\n## Company Guidelines\n\n1. Follow coding standards\n2. Write unit tests\n3. Update documentation"
      }
    }
  }
}
```

### 3. Конфигурация для команды разработки

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "env": {
        "DATA_DIR": "/shared/project/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false",
        "MCP_PROMPT_EXECUTE_TASK_APPEND": "\n\n## Team Standards\n\n- Use TypeScript strict mode\n- Follow ESLint rules\n- Write JSDoc comments\n- Create unit tests for all functions\n- Update README.md if needed"
      }
    }
  }
}
```

### 4. Конфигурация для безопасности

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["/path/to/mcp-shrimp-task-manager/dist/index.js"],
      "env": {
        "DATA_DIR": "/secure/project/data",
        "TEMPLATES_USE": "en",
        "MCP_PROMPT_ANALYZE_TASK": "## Security-Oriented Task Analysis\n\nPlease conduct a comprehensive security analysis for the following task:\n\n**Task Summary:**\n{summary}\n\n**Initial Concept:**\n{initialConcept}\n\nDuring the analysis, please pay special attention to:\n1. Code injection risks\n2. Permission management issues\n3. Data validation and sanitization\n4. Security risks of third-party dependencies\n5. Potential configuration errors\n\nFor each potential issue, please provide:\n- Issue description\n- Impact level (Low/Medium/High)\n- Recommended solution\n\n{previousAnalysis}"
      }
    }
  }
}
```

## 🌐 Многоязычная поддержка

### Переключение языка шаблонов

#### Английский (по умолчанию)
```bash
export TEMPLATES_USE=en
```

#### Традиционный китайский
```bash
export TEMPLATES_USE=zh
```

#### Кастомные шаблоны
```bash
export TEMPLATES_USE=my_custom_templates
```

### Создание кастомных шаблонов

1. **Скопируйте существующий шаблон:**
```bash
cp -r src/prompts/templates_en /path/to/DATA_DIR/my_custom_templates
```

2. **Отредактируйте файлы шаблонов:**
```bash
# Структура кастомных шаблонов
my_custom_templates/
├── planTask/
│   └── index.md
├── analyzeTask/
│   └── index.md
├── executeTask/
│   └── index.md
├── verifyTask/
│   └── index.md
└── ...
```

3. **Укажите кастомную директорию:**
```json
{
  "env": {
    "DATA_DIR": "/path/to/project/data",
    "TEMPLATES_USE": "my_custom_templates"
  }
}
```

## 📝 Кастомизация промптов

### Параметры для различных функций

#### planTask
- `{description}` - Описание задачи
- `{requirements}` - Требования к задаче
- `{existingTasksReference}` - Ссылка на существующие задачи
- `{completedTasks}` - Список завершенных задач
- `{pendingTasks}` - Список ожидающих задач
- `{memoryDir}` - Директория памяти задач

#### analyzeTask
- `{summary}` - Резюме задачи
- `{initialConcept}` - Начальная концепция
- `{previousAnalysis}` - Результаты предыдущего анализа

#### executeTask
- `{task}` - Детали задачи
- `{complexityAssessment}` - Оценка сложности
- `{relatedFilesSummary}` - Резюме связанных файлов
- `{dependencyTasks}` - Зависимые задачи
- `{potentialFiles}` - Потенциально связанные файлы

#### verifyTask
- `{task}` - Детали задачи

#### listTasks
- `{status}` - Статус задач
- `{tasks}` - Задачи, сгруппированные по статусу
- `{allTasks}` - Все задачи

### Примеры кастомизации

#### Упрощенный список задач
```bash
MCP_PROMPT_LIST_TASKS="# Task Overview\n\n## Pending Tasks\n{tasks.pending}\n\n## In-Progress Tasks\n{tasks.in_progress}\n\n## Completed Tasks\n{tasks.completed}"
```

#### Добавление корпоративных стандартов
```bash
MCP_PROMPT_EXECUTE_TASK_APPEND="\n\n## Company-Specific Guidelines\n\nWhen executing tasks, please follow these principles:\n1. Keep code consistent with company style guide\n2. All new features must have corresponding unit tests\n3. Documentation must use company standard templates\n4. Ensure all user interface elements comply with brand design specifications"
```

## 🔒 Безопасность и лучшие практики

### Рекомендации по безопасности

1. **Используйте абсолютные пути для DATA_DIR**
```json
{
  "env": {
    "DATA_DIR": "/absolute/path/to/project/data"
  }
}
```

2. **Ограничьте доступ к директории данных**
```bash
chmod 750 /path/to/project/data
```

3. **Не храните чувствительные данные в промптах**
```bash
# Плохо
MCP_PROMPT_PLAN_TASK="Use API key: sk-1234567890"

# Хорошо
MCP_PROMPT_PLAN_TASK="Use environment variable for API key"
```

### Лучшие практики

1. **Используйте .env файлы для локальной разработки**
```bash
# .env
DATA_DIR=/Users/<USER>/projects/my-project/data
TEMPLATES_USE=en
ENABLE_GUI=false
```

2. **Версионируйте конфигурации**
```bash
# Создайте отдельные конфигурации для разных сред
config/
├── development.json
├── staging.json
└── production.json
```

3. **Документируйте кастомные промпты**
```markdown
## Custom Prompts Documentation

### MCP_PROMPT_PLAN_TASK
Purpose: Enhanced task planning with security focus
Parameters: {description}, {requirements}
Last updated: 2024-01-15
```

## 🚀 Быстрый старт

### Минимальная конфигурация
```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "env": {
        "DATA_DIR": "/absolute/path/to/your/project/data"
      }
    }
  }
}
```

### Проверка конфигурации
1. Убедитесь, что `DATA_DIR` существует и доступен для записи
2. Проверьте, что все пути абсолютные
3. Протестируйте базовые команды: `list_tasks`, `plan_task`

## 🔧 Устранение неполадок

### Частые проблемы

1. **Ошибка "DATA_DIR not found"**
   - Убедитесь, что путь абсолютный
   - Проверьте права доступа к директории

2. **Промпты не применяются**
   - Проверьте синтаксис JSON
   - Убедитесь в правильности экранирования символов

3. **Шаблоны не найдены**
   - Проверьте значение `TEMPLATES_USE`
   - Убедитесь, что кастомная директория существует
