# 🚀 Shrimp Task Manager - Настройка для проекта

## 🎯 Конфигурация для твоего проекта

Основываясь на найденной конфигурации в `servers_config_backup.json`, вот готовая настройка Shrimp Task Manager для твоего проекта.

## 📋 Текущая конфигурация

### Найденная конфигурация в проекте

```json
{
  "mcp-shrimp-task-manager": {
    "command": "npx",
    "args": ["/Users/<USER>/Projects/handmade/node-native/mcp-shrimp-task-manager/dist/index.js"],
    "env": {
      "DATA_DIR": "/Volumes/SAB500/VideoLingo",
      "TEMPLATES_USE": "en",
      "ENABLE_GUI": "false"
    }
  }
}
```

## ⚙️ Рекомендуемая конфигурация

### 1. Д<PERSON><PERSON> <PERSON> Desktop

Создай или обнови файл `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "n8n": {
      "command": "node",
      "args": ["/Users/<USER>/Projects/MCP_TOOLS/mcp-n8n-server/dist/index.js"],
      "env": {
        "N8N_BASE_URL": "http://localhost:5678",
        "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.azIf9ftK8Dv81GlqPXFsB_M-NepXBH00VC1mW4VpgYo"
      }
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "env": {
        "DATA_DIR": "/Users/<USER>/Projects/MCP_TOOLS/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false",
        "MCP_PROMPT_PLAN_TASK_APPEND": "\n\n## Project Guidelines\n\n- Use TypeScript with strict mode\n- Follow clean code principles\n- Write comprehensive tests\n- Document all public APIs\n- Use conventional commits",
        "MCP_PROMPT_EXECUTE_TASK_APPEND": "\n\n## Execution Standards\n\n- Test all changes before committing\n- Update documentation if needed\n- Follow the established code style\n- Consider performance implications\n- Ensure backward compatibility"
      }
    }
  }
}
```

### 2. Для MCP Omni Connect

Обнови файл `mcp_omni_connect/servers_config.json`:

```json
{
  "AgentConfig": {
    "tool_call_timeout": 45,
    "max_steps": 20,
    "request_limit": 2000,
    "total_tokens_limit": 150000
  },
  "LLM": {
    "provider": "ollama",
    "model": "llama3.2:1b",
    "temperature": 0.3,
    "max_tokens": 8000,
    "max_context_length": 128000,
    "top_p": 0.95,
    "base_url": "http://localhost:11434"
  },
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "n8n": {
      "command": "node",
      "args": ["/Users/<USER>/Projects/MCP_TOOLS/mcp-n8n-server/dist/index.js"],
      "env": {
        "N8N_BASE_URL": "http://localhost:5678",
        "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.azIf9ftK8Dv81GlqPXFsB_M-NepXBH00VC1mW4VpgYo"
      }
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "env": {
        "DATA_DIR": "/Users/<USER>/Projects/MCP_TOOLS/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false",
        "MCP_PROMPT_PLAN_TASK_APPEND": "\n\n## MCP Tools Project Guidelines\n\n- Focus on MCP server development\n- Ensure compatibility with multiple clients\n- Document all tools and resources\n- Follow TypeScript best practices\n- Test with real MCP clients",
        "MCP_PROMPT_EXECUTE_TASK_APPEND": "\n\n## MCP Development Standards\n\n- Test with Claude Desktop and other MCP clients\n- Validate all tool schemas\n- Ensure proper error handling\n- Update documentation\n- Consider cross-platform compatibility"
      }
    },
    "godly-filesystem": {
      "command": "node",
      "args": ["/Users/<USER>/Projects/handmade/node-rewritten/filesystem-manager/index.js"]
    }
  }
}
```

## 📁 Структура директорий

### Создание директории данных

```bash
# Создай директорию для данных Shrimp Task Manager
mkdir -p /Users/<USER>/Projects/MCP_TOOLS/data

# Установи правильные права доступа
chmod 755 /Users/<USER>/Projects/MCP_TOOLS/data

# Создай поддиректории для организации
mkdir -p /Users/<USER>/Projects/MCP_TOOLS/data/{tasks,templates,logs}
```

### Структура проекта

```
/Users/<USER>/Projects/MCP_TOOLS/
├── data/                           # Данные Shrimp Task Manager
│   ├── tasks/                      # Файлы задач
│   ├── templates/                  # Кастомные шаблоны
│   └── logs/                       # Логи выполнения
├── mcp-n8n-server/                # n8n MCP сервер
├── mcp-shrimp-task-manager-docs/   # Документация Shrimp Task Manager
└── mcp_omni_connect/              # MCP Omni Connect
```

## 🛠️ Установка и настройка

### 1. Установка Shrimp Task Manager

```bash
# Глобальная установка (рекомендуется)
npm install -g mcp-shrimp-task-manager

# Или через npx (будет загружаться при каждом запуске)
npx -y mcp-shrimp-task-manager --help
```

### 2. Проверка установки

```bash
# Проверь, что команда доступна
npx -y mcp-shrimp-task-manager --version

# Проверь конфигурацию
echo $DATA_DIR
```

### 3. Создание кастомных шаблонов (опционально)

```bash
# Скопируй базовые шаблоны для кастомизации
mkdir -p /Users/<USER>/Projects/MCP_TOOLS/data/templates/custom_en

# Создай кастомный шаблон планирования
cat > /Users/<USER>/Projects/MCP_TOOLS/data/templates/custom_en/planTask.md << 'EOF'
# MCP Tools Project Task Planning

## Task Description
{description}

## Requirements and Constraints
{requirements}

## MCP-Specific Considerations
- Ensure compatibility with MCP protocol
- Consider multiple client support (Claude Desktop, Cursor, etc.)
- Plan for proper error handling and validation
- Think about resource management and tool organization

## Existing Tasks Reference
{existingTasksReference}

Please create a detailed plan following MCP development best practices.
EOF
```

## 🎯 Быстрый старт

### 1. Инициализация проекта

```bash
# Перейди в директорию проекта
cd /Users/<USER>/Projects/MCP_TOOLS

# Инициализируй правила проекта через Shrimp Task Manager
# (используй через Claude Desktop или MCP Omni Connect)
```

### 2. Первые команды

После настройки конфигурации, попробуй эти команды в Claude Desktop:

```
Инициализируй правила проекта для MCP Tools разработки
```

```
Создай план для разработки нового MCP сервера
```

```
Покажи все текущие задачи
```

```
Проанализируй задачу создания документации
```

## 🔧 Кастомизация для MCP разработки

### Специализированные промпты

```json
{
  "env": {
    "MCP_PROMPT_ANALYZE_TASK": "## MCP Server Analysis\n\nAnalyze the following MCP-related task:\n\n**Task Summary:** {summary}\n**Initial Concept:** {initialConcept}\n\n### MCP-Specific Analysis Points:\n1. Protocol compliance (JSON-RPC 2.0)\n2. Tool/resource schema validation\n3. Error handling patterns\n4. Client compatibility\n5. Performance considerations\n6. Security implications\n\n### Previous Analysis:\n{previousAnalysis}",
    
    "MCP_PROMPT_VERIFY_TASK": "## MCP Task Verification\n\n**Task Details:** {task}\n\n### Verification Checklist:\n- [ ] MCP protocol compliance\n- [ ] Schema validation\n- [ ] Error handling\n- [ ] Documentation updated\n- [ ] Client testing completed\n- [ ] Performance acceptable\n\n### Scoring Guidelines:\n- 90-100: Production ready\n- 80-89: Minor issues to address\n- 70-79: Significant improvements needed\n- Below 70: Major rework required\n\nProvide score (0-100) and detailed summary."
  }
}
```

## 📊 Мониторинг и обслуживание

### Регулярные задачи

```bash
# Еженедельная очистка логов
find /Users/<USER>/Projects/MCP_TOOLS/data/logs -name "*.log" -mtime +7 -delete

# Резервное копирование задач
cp -r /Users/<USER>/Projects/MCP_TOOLS/data/tasks /Users/<USER>/Projects/MCP_TOOLS/data/tasks_backup_$(date +%Y%m%d)

# Проверка размера директории данных
du -sh /Users/<USER>/Projects/MCP_TOOLS/data
```

### Обновление конфигурации

```bash
# Создай скрипт для быстрого обновления конфигурации
cat > /Users/<USER>/Projects/MCP_TOOLS/update_config.sh << 'EOF'
#!/bin/bash

CONFIG_FILE="$HOME/Library/Application Support/Claude/claude_desktop_config.json"
BACKUP_FILE="$HOME/Library/Application Support/Claude/claude_desktop_config.json.backup.$(date +%Y%m%d)"

# Создай резервную копию
cp "$CONFIG_FILE" "$BACKUP_FILE"

echo "Configuration backed up to: $BACKUP_FILE"
echo "You can now safely update the configuration file"
EOF

chmod +x /Users/<USER>/Projects/MCP_TOOLS/update_config.sh
```

## 🚀 Следующие шаги

1. **Настрой конфигурацию** согласно приведенным примерам
2. **Перезапусти Claude Desktop** для загрузки новой конфигурации
3. **Протестируй базовые команды** Shrimp Task Manager
4. **Создай первый проект** с правилами разработки
5. **Настрой кастомные шаблоны** под свои потребности

## 🆘 Устранение неполадок

### Частые проблемы

1. **Shrimp Task Manager не отвечает**
   - Проверь, что `DATA_DIR` существует и доступен для записи
   - Убедись, что путь абсолютный
   - Проверь логи Claude Desktop

2. **Команды не выполняются**
   - Проверь синтаксис JSON конфигурации
   - Убедись, что все пути корректны
   - Перезапусти Claude Desktop

3. **Кастомные промпты не работают**
   - Проверь экранирование символов в JSON
   - Убедись, что переменные окружения установлены правильно

### Логи и отладка

```bash
# Проверь логи Claude Desktop (macOS)
tail -f ~/Library/Logs/Claude/claude_desktop.log

# Проверь права доступа к директории данных
ls -la /Users/<USER>/Projects/MCP_TOOLS/data

# Тестируй Shrimp Task Manager напрямую
npx -y mcp-shrimp-task-manager --help
```
