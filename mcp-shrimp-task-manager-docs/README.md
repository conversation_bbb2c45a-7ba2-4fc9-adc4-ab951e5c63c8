# 🦐 Shrimp Task Manager - Полная документация

## 🎯 Обзор

**Shrimp Task Manager** - это продвинутый MCP (Model Context Protocol) сервер для управления задачами, специально разработанный для AI агентов. Он обеспечивает структурированный подход к планированию, анализу и выполнению задач с поддержкой цепочки рассуждений (chain-of-thought), рефлексии и консистентности стиля.

### 🌟 Ключевые особенности

- **🧠 Chain-of-thought reasoning** - пошаговое рассуждение для сложных проблем
- **🔄 Iterative refinement** - итеративное улучшение решений
- **📊 Dependency tracking** - отслеживание зависимостей между задачами
- **🎨 Style consistency** - поддержание консистентности стиля кода
- **🌐 Multi-language support** - поддержка английского и китайского языков
- **⚙️ Customizable prompts** - настраиваемые промпты для различных сценариев

## 🛠️ Архитектура

### Основные компоненты

1. **Task Planning** - планирование задач
2. **Task Analysis** - глубокий анализ требований
3. **Task Execution** - выполнение задач
4. **Task Verification** - проверка выполнения
5. **Project Management** - управление проектом

### Диаграмма потока выполнения задач

```
┌─────────────────┐    ┌──────────────────┐    ┌──────────────────┐
│ execute_task    │───▶│ executeTask      │───▶│ processExecution │
│ Schema          │    │ (taskTools.ts)   │    │ (taskModel.ts)   │
└─────────────────┘    └──────────────────┘    └──────────────────┘
                                │                         │
                                ▼                         ▼
                        ┌───────────────────────────────────────┐
                        │        Validate Parameters            │
                        └───────────────────────────────────────┘
                                │
                                ▼
                        ┌───────────────────────────────────────┐
                        │      Check Dependency Status          │
                        └───────────────────────────────────────┘
                                │
                                ▼
                        ┌───────────────────────────────────────┐
                        │   Load Task Context (Plan, Analysis)  │
                        └───────────────────────────────────────┘
                                │
                                ▼
                        ┌───────────────────────────────────────┐
                        │      Load Related Files Context       │
                        └───────────────────────────────────────┘
                                │
                                ▼
                        ┌───────────────────────────────────────┐
                        │      Mark Task as In Progress         │
                        └───────────────────────────────────────┘
```

## 📦 Установка

### Метод 1: Автоматическая установка через Smithery CLI

```bash
npx -y @smithery/cli install @cjo4m06/mcp-shrimp-task-manager --client claude
```

### Метод 2: Ручная установка

```bash
# Клонирование репозитория
git clone https://github.com/cjo4m06/mcp-shrimp-task-manager.git
cd mcp-shrimp-task-manager

# Установка зависимостей
npm install

# Сборка проекта
npm run build
```

## ⚙️ Конфигурация

### Базовая конфигурация для Claude Desktop

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["/path/to/mcp-shrimp-task-manager/dist/index.js"],
      "env": {
        "DATA_DIR": "/path/to/project/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

### Конфигурация для Cursor IDE (глобально)

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "env": {
        "DATA_DIR": "/mcp-shrimp-task-manager/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

### Конфигурация для конкретного проекта

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["/path/to/mcp-shrimp-task-manager/dist/index.js"],
      "env": {
        "DATA_DIR": "/absolute/path/to/project/data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

### Переменные окружения

| Переменная | Описание | Значение по умолчанию |
|------------|----------|----------------------|
| `DATA_DIR` | Абсолютный путь к директории данных проекта | - |
| `TEMPLATES_USE` | Язык шаблонов (`en`, `zh`, или кастомная директория) | `en` |
| `ENABLE_GUI` | Включение графического интерфейса | `false` |
| `MCP_PROMPT_*` | Кастомизация промптов | - |

## 🔧 Доступные инструменты

### Планирование задач

#### `plan_task`
**Описание**: Начать планирование задач
**Использование**: Создание структурированного плана выполнения задач

```
plan_task({
  description: "Описание задачи",
  requirements: "Требования и ограничения"
})
```

### Анализ задач

#### `analyze_task`
**Описание**: Глубокий анализ требований задачи
**Использование**: Детальный анализ задачи с предварительным концептом

```
analyze_task({
  summary: "Краткое описание задачи",
  initialConcept: "Начальная концепция решения"
})
```

#### `process_thought`
**Описание**: Пошаговое рассуждение для сложных проблем
**Использование**: Структурированный анализ сложных задач

### Оценка решений

#### `reflect_task`
**Описание**: Рефлексия и улучшение концепций решений
**Использование**: Анализ и улучшение предложенных решений

```
reflect_task({
  summary: "Резюме анализа",
  analysis: "Результаты анализа"
})
```

### Исследование

#### `research_mode`
**Описание**: Вход в режим систематического технического исследования
**Использование**: Глубокое исследование технических вопросов

### Управление проектом

#### `init_project_rules`
**Описание**: Инициализация или обновление стандартов и правил проекта
**Использование**: Создание файла `shrimp-rules.md` с правилами проекта

### Управление задачами

#### `split_tasks`
**Описание**: Разбиение задач на подзадачи
**Использование**: Декомпозиция сложных задач

#### `list_tasks`
**Описание**: Отображение всех задач и их статусов
**Использование**: Просмотр текущего состояния всех задач

#### `query_task`
**Описание**: Поиск и фильтрация задач
**Использование**: Поиск задач по различным критериям

#### `get_task_detail`
**Описание**: Отображение полных деталей задачи
**Использование**: Получение подробной информации о конкретной задаче

```
get_task_detail({taskId})
```

#### `delete_task`
**Описание**: Удаление незавершенных задач
**Использование**: Удаление ненужных или ошибочных задач

### Выполнение задач

#### `execute_task`
**Описание**: Выполнение конкретных задач
**Использование**: Запуск выполнения задачи

#### `verify_task`
**Описание**: Проверка завершения задачи
**Использование**: Валидация результатов выполнения

```
verify_task({
  taskId: "ID задачи",
  score: 85,
  summary: "Резюме выполнения"
})
```

## 🎨 Кастомизация промптов

### Языковые шаблоны

```bash
# Использование английских шаблонов
export TEMPLATES_USE=en

# Использование китайских шаблонов
export TEMPLATES_USE=zh

# Использование кастомных шаблонов
export TEMPLATES_USE=my_custom_templates
```

### Кастомизация промптов планирования

```bash
# Через переменные окружения
MCP_PROMPT_PLAN_TASK="## Custom Task Planning\n\nPlease plan the task based on the following information:\n\n{description}\n\nRequirements: {requirements}\n"
```

```json
// Через mcp.json
{
  "env": {
    "MCP_PROMPT_PLAN_TASK": "## Custom Task Planning\n\nPlease plan the task based on the following information:\n\n{description}\n\nRequirements: {requirements}\n"
  }
}
```

### Добавление дополнительных инструкций

```bash
# Добавление к промпту выполнения задач
MCP_PROMPT_EXECUTE_TASK_APPEND="\n\n## Company-Specific Guidelines\n\nWhen executing tasks, please follow these principles:\n1. Keep code consistent with company style guide\n2. All new features must have corresponding unit tests"
```

## 📊 Рабочий процесс

### Типичный workflow

1. **Планирование** (`plan_task`)
   - Анализ требований
   - Создание структурированного плана
   - Определение зависимостей

2. **Анализ** (`analyze_task`)
   - Глубокий анализ задачи
   - Оценка сложности
   - Выявление потенциальных проблем

3. **Рефлексия** (`reflect_task`)
   - Улучшение концепции
   - Оптимизация подхода

4. **Разбиение** (`split_tasks`)
   - Декомпозиция на подзадачи
   - Установка приоритетов

5. **Выполнение** (`execute_task`)
   - Реализация решения
   - Следование плану

6. **Проверка** (`verify_task`)
   - Валидация результатов
   - Оценка качества

### Режимы работы

#### TaskPlanner Mode
```
You are a professional task planning expert. You must interact with users, analyze their needs, and collect project-related information. Finally, you must use "plan_task" to create tasks.
```

#### TaskExecutor Mode
```
You are a professional task execution expert. When a user specifies a task to execute, use "execute_task" to execute the task.
```
