# 🦐 Shrimp Task Manager - Полная документация

## 📚 Оглавление документации

Добро пожаловать в полную документацию по **Shrimp Task Manager** - продвинутому MCP серверу для управления задачами, специально разработанному для AI агентов.

### 📖 Основные документы

#### 1. 📋 [README.md](README.md) - Основная документация
- 🎯 Обзор и ключевые особенности
- 🛠️ Архитектура системы
- 📦 Установка и базовая настройка
- 🔧 Доступные инструменты (14 инструментов)
- 📊 Рабочие процессы и режимы работы

#### 2. ⚙️ [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) - Руководство по конфигурации
- 📁 Структура конфигурации
- 🔧 Примеры конфигурации для разных сред
- 🌐 Многоязычная поддержка (en/zh/custom)
- 📝 Кастомизация промптов
- 🔒 Безопасность и лучшие практики

#### 3. 🛠️ [TOOLS_REFERENCE.md](TOOLS_REFERENCE.md) - Справочник инструментов
- 📝 Планирование задач (`plan_task`)
- 🔍 Анализ задач (`analyze_task`, `process_thought`)
- 🎨 Оценка решений (`reflect_task`)
- 🔬 Исследование (`research_mode`)
- 📊 Управление проектом (`init_project_rules`)
- 📋 Управление задачами (`split_tasks`, `list_tasks`, `query_task`, etc.)
- ⚡ Выполнение задач (`execute_task`, `verify_task`)

#### 4. 🚀 [WORKFLOW_EXAMPLES.md](WORKFLOW_EXAMPLES.md) - Примеры рабочих процессов
- 📋 Базовые рабочие процессы
- 🏢 Корпоративные сценарии
- 🔄 Итеративные процессы
- 📈 Мониторинг и управление
- 🎓 Обучающие сценарии
- 🚀 Продвинутые техники

#### 5. 📋 [DEVELOPMENT_STANDARDS.md](DEVELOPMENT_STANDARDS.md) - Стандарты разработки
- 🏗️ Архитектурные принципы
- 💻 Стандарты кодирования
- 🧪 Стандарты тестирования
- 🔒 Стандарты безопасности
- 📈 Стандарты производительности
- 📊 Мониторинг и логирование

#### 6. 🚀 [PROJECT_SETUP.md](PROJECT_SETUP.md) - Настройка для проекта
- 📋 Текущая конфигурация проекта
- ⚙️ Рекомендуемые настройки
- 📁 Структура директорий
- 🛠️ Установка и настройка
- 🎯 Быстрый старт

## 🎯 Быстрая навигация

### 🚀 Для начинающих
1. Начни с [README.md](README.md) для общего понимания
2. Изучи [PROJECT_SETUP.md](PROJECT_SETUP.md) для настройки
3. Попробуй примеры из [WORKFLOW_EXAMPLES.md](WORKFLOW_EXAMPLES.md)

### ⚙️ Для настройки
1. [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) - полное руководство по конфигурации
2. [PROJECT_SETUP.md](PROJECT_SETUP.md) - специфичная настройка для проекта

### 🛠️ Для разработчиков
1. [TOOLS_REFERENCE.md](TOOLS_REFERENCE.md) - полный справочник инструментов
2. [DEVELOPMENT_STANDARDS.md](DEVELOPMENT_STANDARDS.md) - стандарты разработки
3. [WORKFLOW_EXAMPLES.md](WORKFLOW_EXAMPLES.md) - продвинутые техники

## 🔧 Основные возможности

### 📊 Управление жизненным циклом задач
- **Планирование** → **Анализ** → **Рефлексия** → **Выполнение** → **Проверка**
- Автоматическое разбиение сложных задач на подзадачи
- Отслеживание зависимостей между задачами
- Оценка сложности и рисков

### 🧠 Интеллектуальный анализ
- Chain-of-thought reasoning для сложных проблем
- Итеративное улучшение решений
- Систематическое техническое исследование
- Анализ безопасности и производительности

### 🎨 Гибкая кастомизация
- Многоязычная поддержка (английский, китайский, кастомные)
- Настраиваемые промпты для любых сценариев
- Корпоративные стандарты и правила
- Интеграция с существующими процессами

## 📋 Категории инструментов

| Категория | Инструменты | Описание |
|-----------|-------------|----------|
| **📝 Task Planning** | `plan_task` | Планирование и структурирование задач |
| **🔍 Task Analysis** | `analyze_task`, `process_thought` | Глубокий анализ и рассуждения |
| **🎨 Solution Assessment** | `reflect_task` | Оценка и улучшение решений |
| **🔬 Research** | `research_mode` | Систематическое исследование |
| **📊 Project Management** | `init_project_rules` | Управление стандартами проекта |
| **📋 Task Management** | `split_tasks`, `list_tasks`, `query_task`, `get_task_detail`, `delete_task` | Управление задачами |
| **⚡ Task Execution** | `execute_task`, `verify_task` | Выполнение и проверка задач |

## 🌟 Ключевые преимущества

### 🎯 Для AI агентов
- **Структурированный подход** к решению сложных задач
- **Память и контекст** между сессиями
- **Итеративное улучшение** решений
- **Автоматическое планирование** и декомпозиция

### 👥 Для команд разработки
- **Единые стандарты** разработки
- **Отслеживаемость** всех изменений
- **Корпоративная кастомизация** процессов
- **Интеграция** с существующими инструментами

### 🏢 Для проектов
- **Консистентность** в подходах
- **Документирование** решений
- **Управление сложностью** больших проектов
- **Качественный контроль** результатов

## 🚀 Примеры использования

### 🆕 Новый проект
```
1. init_project_rules() - установка стандартов
2. plan_task() - планирование архитектуры
3. split_tasks() - разбиение на этапы
4. execute_task() - реализация
5. verify_task() - проверка качества
```

### 🐛 Исправление багов
```
1. research_mode() - исследование проблемы
2. analyze_task() - анализ причин
3. plan_task() - планирование исправления
4. execute_task() - реализация решения
5. verify_task() - проверка исправления
```

### 🔄 Рефакторинг
```
1. analyze_task() - анализ текущего состояния
2. reflect_task() - оценка улучшений
3. plan_task() - планирование рефакторинга
4. split_tasks() - поэтапная реализация
5. execute_task() - выполнение изменений
```

## 📞 Поддержка и ресурсы

### 📚 Документация
- [GitHub Repository](https://github.com/cjo4m06/mcp-shrimp-task-manager)
- [MCP Protocol Documentation](https://modelcontextprotocol.io/)
- [TypeScript SDK](https://github.com/modelcontextprotocol/typescript-sdk)

### 🛠️ Инструменты
- [Smithery CLI](https://smithery.ai/) для автоматической установки
- [Claude Desktop](https://claude.ai/desktop) для интеграции
- [Cursor IDE](https://cursor.sh/) для разработки

### 🤝 Сообщество
- [MCP Community](https://github.com/modelcontextprotocol/community)
- [Discord Server](https://discord.gg/modelcontextprotocol)
- [GitHub Discussions](https://github.com/cjo4m06/mcp-shrimp-task-manager/discussions)

## 🔄 Обновления документации

**Последнее обновление**: 2024-12-23  
**Версия документации**: 1.0.0  
**Совместимость**: mcp-shrimp-task-manager v1.0.19+

### 📝 История изменений
- **v1.0.0** (2024-12-23): Создание полной документации
  - Основная документация и обзор
  - Руководство по конфигурации
  - Справочник инструментов
  - Примеры рабочих процессов
  - Стандарты разработки
  - Настройка для конкретного проекта

---

**💡 Совет**: Начни с [PROJECT_SETUP.md](PROJECT_SETUP.md) для быстрой настройки, затем изучи [WORKFLOW_EXAMPLES.md](WORKFLOW_EXAMPLES.md) для практических примеров использования.
