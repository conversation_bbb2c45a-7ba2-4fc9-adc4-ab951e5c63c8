# 🚀 Shrimp Task Manager - Примеры рабочих процессов

## 🎯 Обзор

Этот документ содержит практические примеры использования Shrimp Task Manager для различных сценариев разработки. Каждый пример включает пошаговые инструкции и реальные команды.

## 📋 Базовые рабочие процессы

### 1. 🆕 Создание нового проекта

#### Шаг 1: Инициализация правил проекта
```
init_project_rules({
  projectType: "React TypeScript Application",
  standards: ["TypeScript", "ESLint", "Prettier", "Jest", "React Testing Library"]
})
```

#### Шаг 2: Планирование основных задач
```
plan_task({
  description: "Создать React приложение для управления задачами",
  requirements: "TypeScript, React 18, Material-UI, Redux Toolkit, Jest для тестирования"
})
```

#### Шаг 3: Анализ задачи
```
analyze_task({
  summary: "React приложение для управления задачами",
  initialConcept: "SPA с компонентной архитектурой, состояние через Redux, UI на Material-UI"
})
```

#### Шаг 4: Рефлексия и улучшение
```
reflect_task({
  summary: "Анализ архитектуры React приложения",
  analysis: "Рассмотрены варианты состояния, выбран Redux Toolkit для сложной логики"
})
```

---

### 2. 🔧 Разработка API

#### Шаг 1: Планирование API
```
plan_task({
  description: "Создать REST API для системы управления пользователями",
  requirements: "Node.js, Express, MongoDB, JWT аутентификация, валидация данных, документация OpenAPI"
})
```

#### Шаг 2: Разбиение на подзадачи
```
split_tasks({
  taskId: "api-development-task",
  maxSubtasks: 6,
  granularity: "detailed"
})
```

**Результат разбиения:**
- Настройка проекта и зависимостей
- Создание моделей данных
- Реализация аутентификации
- CRUD операции для пользователей
- Валидация и обработка ошибок
- Документация и тестирование

#### Шаг 3: Выполнение подзадач
```
# Выполняем первую подзадачу
execute_task({ taskId: "setup-project-dependencies" })

# Проверяем результат
verify_task({
  taskId: "setup-project-dependencies",
  score: 90,
  summary: "Проект настроен, зависимости установлены, структура папок создана"
})
```

---

### 3. 🐛 Исправление багов

#### Шаг 1: Исследование проблемы
```
research_mode({
  topic: "Медленная загрузка страниц в React приложении",
  scope: "Анализ производительности, профилирование, оптимизация"
})
```

#### Шаг 2: Структурированный анализ
```
process_thought({
  problem: "Страницы загружаются более 3 секунд",
  context: "React SPA с большим количеством компонентов и API вызовов"
})
```

#### Шаг 3: Планирование исправления
```
plan_task({
  description: "Оптимизировать производительность React приложения",
  requirements: "Уменьшить время загрузки до 1 секунды, сохранить функциональность"
})
```

#### Шаг 4: Выполнение оптимизации
```
execute_task({ taskId: "performance-optimization" })
```

---

## 🏢 Корпоративные сценарии

### 1. 📊 Разработка для команды

#### Настройка корпоративных стандартов
```
init_project_rules({
  projectType: "Enterprise Web Application",
  standards: [
    "TypeScript strict mode",
    "ESLint corporate rules",
    "Husky pre-commit hooks",
    "Jest + Testing Library",
    "Storybook for components",
    "Docker containerization"
  ]
})
```

#### Планирование с учетом команды
```
plan_task({
  description: "Создать модуль отчетности для корпоративной системы",
  requirements: `
    - Интеграция с существующей системой аутентификации
    - Поддержка экспорта в PDF/Excel
    - Роли и права доступа
    - Аудит действий пользователей
    - Соответствие корпоративным стандартам безопасности
  `
})
```

### 2. 🔒 Безопасная разработка

#### Анализ с фокусом на безопасность
```
analyze_task({
  summary: "Модуль обработки платежей",
  initialConcept: "Интеграция с Stripe API, шифрование данных, PCI DSS compliance"
})
```

**С кастомным промптом безопасности:**
```json
{
  "env": {
    "MCP_PROMPT_ANALYZE_TASK": "## Security-Oriented Task Analysis\n\nPlease conduct a comprehensive security analysis for the following task:\n\n**Task Summary:**\n{summary}\n\n**Initial Concept:**\n{initialConcept}\n\nDuring the analysis, please pay special attention to:\n1. Code injection risks\n2. Permission management issues\n3. Data validation and sanitization\n4. Security risks of third-party dependencies\n5. Potential configuration errors"
  }
}
```

---

## 🔄 Итеративные процессы

### 1. 🎨 Прототипирование

#### Цикл 1: Базовый прототип
```
plan_task({
  description: "Создать MVP для мобильного приложения заказа еды",
  requirements: "React Native, базовый UI, список ресторанов, корзина"
})

execute_task({ taskId: "mvp-food-app" })

verify_task({
  taskId: "mvp-food-app",
  score: 75,
  summary: "MVP создан, но нужно улучшить UX и добавить валидацию форм"
})
```

#### Цикл 2: Улучшения
```
reflect_task({
  summary: "Анализ MVP приложения",
  analysis: "Выявлены проблемы с UX, нужна валидация, улучшение дизайна"
})

plan_task({
  description: "Улучшить UX и добавить валидацию в приложение заказа еды",
  requirements: "Улучшенный дизайн, валидация форм, обработка ошибок"
})
```

### 2. 🧪 A/B тестирование

```
plan_task({
  description: "Реализовать A/B тестирование для кнопки покупки",
  requirements: "Две версии кнопки, метрики конверсии, статистическая значимость"
})

split_tasks({
  taskId: "ab-testing-implementation",
  maxSubtasks: 4
})
```

---

## 📈 Мониторинг и управление

### 1. 📊 Отслеживание прогресса

#### Ежедневный обзор
```
list_tasks()
```

#### Поиск проблемных задач
```
query_task({
  query: "failed OR blocked",
  filters: { status: ["failed", "blocked"] }
})
```

#### Детальный анализ проблемы
```
get_task_detail({ taskId: "problematic-task-id" })
```

### 2. 🔄 Рефакторинг задач

#### Удаление устаревших задач
```
delete_task({ taskId: "outdated-task-id" })
```

#### Переосмысление подхода
```
reflect_task({
  summary: "Анализ текущего подхода к архитектуре",
  analysis: "Текущая архитектура не масштабируется, нужен переход на микросервисы"
})
```

---

## 🎓 Обучающие сценарии

### 1. 📚 Изучение новой технологии

```
research_mode({
  topic: "GraphQL с Apollo Server",
  scope: "Основы, лучшие практики, интеграция с React"
})

process_thought({
  problem: "Как лучше структурировать GraphQL схему для сложного приложения",
  context: "Переход с REST API на GraphQL"
})

plan_task({
  description: "Создать GraphQL API для замены существующего REST API",
  requirements: "Apollo Server, схема с типами, резолверы, интеграция с базой данных"
})
```

### 2. 🏗️ Архитектурные решения

```
analyze_task({
  summary: "Выбор архитектуры для высоконагруженного приложения",
  initialConcept: "Микросервисы с Docker, Kubernetes, API Gateway"
})

reflect_task({
  summary: "Анализ микросервисной архитектуры",
  analysis: "Рассмотрены плюсы и минусы, выбран поэтапный переход"
})
```

---

## 🚀 Продвинутые техники

### 1. 🔗 Цепочки задач

```
# Создаем связанные задачи
plan_task({
  description: "Полный цикл разработки фичи авторизации",
  requirements: "Backend API → Frontend компоненты → Тесты → Документация"
})

# Разбиваем на этапы
split_tasks({ taskId: "auth-feature-full-cycle" })

# Выполняем последовательно с проверками
execute_task({ taskId: "auth-backend-api" })
verify_task({ taskId: "auth-backend-api", score: 95 })

execute_task({ taskId: "auth-frontend-components" })
verify_task({ taskId: "auth-frontend-components", score: 88 })
```

### 2. 🎯 Параллельная разработка

```
# Планируем независимые задачи
plan_task({
  description: "Параллельная разработка компонентов UI",
  requirements: "Header, Sidebar, Footer - независимые компоненты"
})

split_tasks({
  taskId: "ui-components-parallel",
  maxSubtasks: 3,
  granularity: "component-level"
})

# Выполняем параллельно (разные разработчики)
execute_task({ taskId: "header-component" })
execute_task({ taskId: "sidebar-component" })
execute_task({ taskId: "footer-component" })
```

---

## 📝 Шаблоны команд

### Быстрые команды для ежедневного использования

```bash
# Обзор текущих задач
list_tasks()

# Поиск задач по ключевому слову
query_task({ query: "authentication" })

# Создание новой задачи
plan_task({
  description: "Описание задачи",
  requirements: "Требования"
})

# Выполнение следующей задачи
execute_task({ taskId: "next-task-id" })

# Проверка завершенной задачи
verify_task({
  taskId: "completed-task-id",
  score: 90,
  summary: "Задача выполнена успешно"
})
```

### Команды для планирования спринта

```bash
# Инициализация правил проекта
init_project_rules({ projectType: "Web Application" })

# Планирование спринта
plan_task({
  description: "Sprint 1: Основная функциональность",
  requirements: "Пользовательская регистрация, основной UI, базовые операции"
})

# Разбиение на задачи спринта
split_tasks({ taskId: "sprint-1-tasks", maxSubtasks: 8 })

# Обзор всех задач спринта
list_tasks({ status: "pending" })
```
