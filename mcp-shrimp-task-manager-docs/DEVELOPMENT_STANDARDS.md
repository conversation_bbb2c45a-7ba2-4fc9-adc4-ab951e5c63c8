# 📋 Shrimp Task Manager - Стандарты разработки

## 🎯 Обзор

Этот документ описывает стандарты разработки, которые Shrimp Task Manager автоматически применяет и рекомендует для проектов. Эти стандарты основаны на лучших практиках индустрии и опыте команды разработчиков.

## 🏗️ Архитектурные принципы

### 1. 🧩 Модульность и разделение ответственности

**Принцип**: Каждый модуль должен иметь единственную ответственность

**Применение в Shrimp Task Manager**:
- Разделение на категории инструментов
- Отдельные модули для планирования, анализа, выполнения
- Независимые промпты для каждой функции

**Рекомендации для проектов**:
```typescript
// ✅ Хорошо: четкое разделение ответственности
class UserService {
  async createUser(userData: UserData): Promise<User> { }
  async getUserById(id: string): Promise<User> { }
}

class EmailService {
  async sendWelcomeEmail(user: User): Promise<void> { }
}

// ❌ Плохо: смешанная ответственность
class UserService {
  async createUser(userData: UserData): Promise<User> { }
  async sendWelcomeEmail(user: User): Promise<void> { } // Не относится к управлению пользователями
}
```

### 2. 🔄 Итеративность и инкрементальность

**Принцип**: Разработка должна происходить итеративно с постоянным улучшением

**Применение в Shrimp Task Manager**:
- Цикл: план → анализ → рефлексия → выполнение → проверка
- Возможность пересмотра и улучшения решений
- Разбиение сложных задач на подзадачи

**Рекомендации**:
1. Начинайте с MVP (Minimum Viable Product)
2. Добавляйте функциональность постепенно
3. Регулярно проводите рефакторинг
4. Собирайте обратную связь на каждой итерации

### 3. 📊 Отслеживаемость и прозрачность

**Принцип**: Все изменения и решения должны быть документированы и отслеживаемы

**Применение в Shrimp Task Manager**:
- Каждая задача имеет полную историю
- Связи между задачами отслеживаются
- Результаты анализа сохраняются

## 💻 Стандарты кодирования

### 1. 🏷️ Соглашения об именовании

#### TypeScript/JavaScript

```typescript
// ✅ Переменные и функции: camelCase
const userName = "john_doe";
function processUserData() {}

// ✅ Классы и интерфейсы: PascalCase
class TaskManager {}
interface ITaskOptions {}

// ✅ Константы: UPPER_SNAKE_CASE
const MAX_RETRIES = 3;
const API_BASE_URL = "https://api.example.com";

// ✅ Файлы: camelCase или kebab-case
taskProcessor.ts
task-utils.ts

// ❌ Избегайте
const Task_Name = "example";        // snake_case для переменных
class taskManager {}               // camelCase для классов
const maxRetries = 3;             // camelCase для констант
```

#### Файловая структура

```
src/
├── components/           # React компоненты
│   ├── common/          # Общие компоненты
│   └── pages/           # Компоненты страниц
├── services/            # Бизнес-логика
├── utils/               # Утилиты
├── types/               # TypeScript типы
├── hooks/               # React хуки
└── constants/           # Константы
```

### 2. 📝 Документация кода

#### JSDoc комментарии

```typescript
/**
 * Обрабатывает задачу по её ID.
 * @param taskId - Уникальный идентификатор задачи
 * @param options - Дополнительные опции обработки
 * @returns Promise с результатом обработки
 * @throws {TaskNotFoundError} Когда задача не найдена
 * @example
 * ```typescript
 * const result = await processTaskById("task-123", { validate: true });
 * ```
 */
async function processTaskById(
  taskId: string, 
  options: ProcessOptions = {}
): Promise<ProcessResult> {
  // implementation
}
```

#### Комментарии к сложной логике

```typescript
// Используем экспоненциальную задержку для повторных попыток
// чтобы избежать перегрузки сервера
const delay = Math.pow(2, attempt) * 1000;
await new Promise(resolve => setTimeout(resolve, delay));
```

### 3. 🎯 Типизация

#### Строгая типизация

```typescript
// ✅ Явные типы для параметров и возвращаемых значений
function greet(name: string): string {
  return `Hello, ${name}`;
}

// ✅ Использование Zod для валидации
import { z } from "zod";

export const TaskSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().optional()
});

export type Task = z.infer<typeof TaskSchema>;

// ❌ Избегайте any
function processData(data: any): any {  // Плохо
  return data;
}
```

#### Интерфейсы и типы

```typescript
// ✅ Используйте интерфейсы для объектов
interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// ✅ Используйте union types для ограниченных значений
type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'failed';

// ✅ Используйте generic types для переиспользования
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}
```

## 🧪 Стандарты тестирования

### 1. 📊 Покрытие кода

**Минимальные требования**:
- Unit tests: 80%+ покрытие
- Integration tests: 60%+ покрытие
- E2E tests: основные пользовательские сценарии

### 2. 🏗️ Структура тестов

```typescript
// ✅ Структура AAA (Arrange, Act, Assert)
describe('TaskService', () => {
  describe('createTask', () => {
    it('should create a new task with valid data', async () => {
      // Arrange
      const taskData = {
        name: 'Test Task',
        description: 'Test Description'
      };
      const mockRepository = createMockRepository();
      const taskService = new TaskService(mockRepository);

      // Act
      const result = await taskService.createTask(taskData);

      // Assert
      expect(result).toBeDefined();
      expect(result.name).toBe(taskData.name);
      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining(taskData)
      );
    });

    it('should throw error when name is empty', async () => {
      // Arrange
      const invalidData = { name: '', description: 'Test' };
      const taskService = new TaskService(createMockRepository());

      // Act & Assert
      await expect(taskService.createTask(invalidData))
        .rejects
        .toThrow('Task name cannot be empty');
    });
  });
});
```

### 3. 🎭 Моки и стабы

```typescript
// ✅ Используйте моки для внешних зависимостей
const mockEmailService = {
  sendEmail: jest.fn().mockResolvedValue(true)
};

// ✅ Создавайте фабрики для тестовых данных
function createTestUser(overrides: Partial<User> = {}): User {
  return {
    id: 'test-id',
    name: 'Test User',
    email: '<EMAIL>',
    createdAt: new Date(),
    ...overrides
  };
}
```

## 🔒 Стандарты безопасности

### 1. 🛡️ Валидация входных данных

```typescript
// ✅ Валидация на границах системы
export async function createUser(req: Request, res: Response) {
  try {
    // Валидация с помощью Zod
    const userData = UserCreateSchema.parse(req.body);
    
    // Дополнительная бизнес-валидация
    if (await userExists(userData.email)) {
      return res.status(400).json({ error: 'User already exists' });
    }

    const user = await userService.createUser(userData);
    res.status(201).json(user);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.errors 
      });
    }
    throw error;
  }
}
```

### 2. 🔐 Обработка секретов

```typescript
// ✅ Используйте переменные окружения
const config = {
  jwtSecret: process.env.JWT_SECRET!,
  dbPassword: process.env.DB_PASSWORD!,
  apiKey: process.env.API_KEY!
};

// ✅ Валидируйте наличие обязательных переменных
function validateConfig() {
  const required = ['JWT_SECRET', 'DB_PASSWORD', 'API_KEY'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

// ❌ Никогда не храните секреты в коде
const API_KEY = "sk-1234567890";  // Плохо!
```

### 3. 🚫 Предотвращение уязвимостей

```typescript
// ✅ Санитизация SQL запросов
const user = await db.query(
  'SELECT * FROM users WHERE email = $1',
  [email]  // Параметризованный запрос
);

// ✅ Санитизация HTML
import DOMPurify from 'dompurify';
const cleanHtml = DOMPurify.sanitize(userInput);

// ✅ Ограничение прав доступа
function requireRole(role: UserRole) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user || req.user.role !== role) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
}
```

## 📈 Стандарты производительности

### 1. ⚡ Оптимизация запросов

```typescript
// ✅ Используйте индексы для частых запросов
// ✅ Ограничивайте количество возвращаемых записей
async function getUsers(page: number = 1, limit: number = 20) {
  const offset = (page - 1) * limit;
  return await db.query(`
    SELECT id, name, email 
    FROM users 
    ORDER BY created_at DESC 
    LIMIT $1 OFFSET $2
  `, [limit, offset]);
}

// ✅ Используйте пагинацию для больших наборов данных
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 2. 🗄️ Кэширование

```typescript
// ✅ Кэширование дорогих операций
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 минут

async function getCachedData(key: string, fetcher: () => Promise<any>) {
  const cached = cache.get(key);
  
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  
  const data = await fetcher();
  cache.set(key, { data, timestamp: Date.now() });
  return data;
}
```

## 📊 Мониторинг и логирование

### 1. 📝 Структурированное логирование

```typescript
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// ✅ Логирование с контекстом
logger.info('User created', {
  userId: user.id,
  email: user.email,
  timestamp: new Date().toISOString(),
  requestId: req.id
});

// ✅ Логирование ошибок с полным контекстом
logger.error('Database connection failed', {
  error: error.message,
  stack: error.stack,
  database: config.database.host,
  timestamp: new Date().toISOString()
});
```

### 2. 📊 Метрики

```typescript
// ✅ Отслеживание ключевых метрик
const metrics = {
  requestCount: 0,
  errorCount: 0,
  responseTime: [] as number[]
};

function trackRequest(req: Request, res: Response, next: NextFunction) {
  const start = Date.now();
  metrics.requestCount++;
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    metrics.responseTime.push(duration);
    
    if (res.statusCode >= 400) {
      metrics.errorCount++;
    }
  });
  
  next();
}
```

## 🚀 Развертывание и DevOps

### 1. 🐳 Контейнеризация

```dockerfile
# ✅ Многоэтапная сборка
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
USER node
CMD ["npm", "start"]
```

### 2. 🔄 CI/CD

```yaml
# ✅ GitHub Actions пример
name: CI/CD Pipeline
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run lint
      - run: npm run test
      - run: npm run build
```
