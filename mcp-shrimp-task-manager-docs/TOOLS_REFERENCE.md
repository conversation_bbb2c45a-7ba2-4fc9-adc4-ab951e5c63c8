# 🛠️ Shrimp Task Manager - Справочник инструментов

## 📋 Обзор инструментов

Shrimp Task Manager предоставляет 14 основных инструментов, организованных по категориям для эффективного управления жизненным циклом задач.

## 🎯 Категории инструментов

### 📝 Планирование задач

#### `plan_task`
**Назначение**: Начать планирование задач  
**Категория**: Task Planning  
**Описание**: Создает структурированный план выполнения задач на основе описания и требований

**Параметры**:
- `description` (string) - Описание задачи
- `requirements` (string) - Требования и ограничения
- `existingTasksReference` (boolean) - Ссылаться ли на существующие задачи

**Пример использования**:
```
plan_task({
  description: "Создать REST API для управления пользователями",
  requirements: "Использовать Node.js, Express, MongoDB. Включить аутентификацию JWT"
})
```

**Поддерживаемые параметры промпта**:
- `{description}` - Описание задачи
- `{requirements}` - Требования к задаче
- `{existingTasksReference}` - Ссылка на существующие задачи
- `{completedTasks}` - Список завершенных задач
- `{pendingTasks}` - Список ожидающих задач
- `{memoryDir}` - Директория памяти задач

---

### 🔍 Анализ задач

#### `analyze_task`
**Назначение**: Глубокий анализ требований задачи  
**Категория**: Task Analysis  
**Описание**: Проводит детальный анализ задачи с оценкой сложности и выявлением потенциальных проблем

**Параметры**:
- `summary` (string) - Краткое описание задачи
- `initialConcept` (string) - Начальная концепция решения

**Пример использования**:
```
analyze_task({
  summary: "API для управления пользователями",
  initialConcept: "Express.js сервер с MongoDB и JWT аутентификацией"
})
```

**Поддерживаемые параметры промпта**:
- `{summary}` - Резюме задачи
- `{initialConcept}` - Начальная концепция
- `{previousAnalysis}` - Результаты предыдущего анализа

#### `process_thought`
**Назначение**: Пошаговое рассуждение для сложных проблем  
**Категория**: Task Analysis  
**Описание**: Структурированный анализ сложных задач с разбивкой на логические шаги

**Пример использования**:
```
process_thought({
  problem: "Оптимизация производительности базы данных",
  context: "Медленные запросы в production среде"
})
```

---

### 🎨 Оценка решений

#### `reflect_task`
**Назначение**: Рефлексия и улучшение концепций решений  
**Категория**: Solution Assessment  
**Описание**: Анализирует предложенные решения и предлагает улучшения

**Параметры**:
- `summary` (string) - Резюме анализа
- `analysis` (string) - Результаты анализа

**Пример использования**:
```
reflect_task({
  summary: "Анализ архитектуры API",
  analysis: "Выявлены проблемы с масштабируемостью и безопасностью"
})
```

**Поддерживаемые параметры промпта**:
- `{summary}` - Резюме задачи
- `{analysis}` - Результаты анализа

---

### 🔬 Исследование

#### `research_mode`
**Назначение**: Систематическое техническое исследование  
**Категория**: Research & Investigation  
**Описание**: Входит в режим глубокого исследования технических вопросов

**Пример использования**:
```
research_mode({
  topic: "Микросервисная архитектура",
  scope: "Паттерны коммуникации между сервисами"
})
```

---

### 📊 Управление проектом

#### `init_project_rules`
**Назначение**: Инициализация стандартов и правил проекта  
**Категория**: Project Management  
**Описание**: Создает или обновляет файл `shrimp-rules.md` с правилами проекта

**Пример использования**:
```
init_project_rules({
  projectType: "Node.js API",
  standards: ["TypeScript", "ESLint", "Jest", "Docker"]
})
```

**Структура создаваемого файла**:
```markdown
# Development Guidelines

## Code Style
- Use TypeScript strict mode
- Follow ESLint rules
- Use camelCase for variables and functions

## Testing
- Write unit tests for all functions
- Use Jest testing framework
- Maintain 80%+ code coverage

## Documentation
- Use JSDoc comments for public functions
- Update README.md for major changes
```

---

### 📋 Управление задачами

#### `split_tasks`
**Назначение**: Разбиение задач на подзадачи  
**Категория**: Task Management  
**Описание**: Декомпозирует сложные задачи на более мелкие, управляемые подзадачи

**Пример использования**:
```
split_tasks({
  taskId: "task-123",
  maxSubtasks: 5,
  granularity: "detailed"
})
```

**Поддерживаемые параметры промпта**:
- `{updateMode}` - Режим обновления
- `{createdTasks}` - Созданные задачи
- `{allTasks}` - Все задачи

#### `list_tasks`
**Назначение**: Отображение всех задач и статусов  
**Категория**: Task Management  
**Описание**: Показывает полный список задач с их текущими статусами

**Параметры**:
- `status` (string, optional) - Фильтр по статусу: `pending`, `in_progress`, `completed`

**Пример использования**:
```
list_tasks()
# или с фильтром
list_tasks({ status: "pending" })
```

**Поддерживаемые параметры промпта**:
- `{status}` - Статус задач
- `{tasks}` - Задачи, сгруппированные по статусу
- `{allTasks}` - Все задачи

#### `query_task`
**Назначение**: Поиск и фильтрация задач  
**Категория**: Task Management  
**Описание**: Поиск задач по различным критериям

**Параметры**:
- `query` (string) - Поисковый запрос
- `filters` (object) - Дополнительные фильтры

**Пример использования**:
```
query_task({
  query: "API authentication",
  filters: { status: "pending", priority: "high" }
})
```

**Поддерживаемые параметры промпта**:
- `{query}` - Содержимое запроса
- `{isId}` - Является ли запрос ID
- `{tasks}` - Результаты запроса
- `{totalTasks}` - Общее количество результатов
- `{page}` - Номер текущей страницы
- `{pageSize}` - Размер страницы
- `{totalPages}` - Общее количество страниц

#### `get_task_detail`
**Назначение**: Получение полных деталей задачи  
**Категория**: Task Management  
**Описание**: Отображает подробную информацию о конкретной задаче

**Параметры**:
- `taskId` (string) - Уникальный идентификатор задачи

**Пример использования**:
```
get_task_detail({ taskId: "task-123" })
```

**Поддерживаемые параметры промпта**:
- `{taskId}` - ID задачи
- `{task}` - Детали задачи
- `{error}` - Сообщение об ошибке (если есть)

#### `delete_task`
**Назначение**: Удаление незавершенных задач  
**Категория**: Task Management  
**Описание**: Удаляет ненужные или ошибочные задачи

**Параметры**:
- `taskId` (string) - ID задачи для удаления

**Пример использования**:
```
delete_task({ taskId: "task-123" })
```

**Ограничения**: Нельзя удалить завершенные задачи

---

### ⚡ Выполнение задач

#### `execute_task`
**Назначение**: Выполнение конкретных задач  
**Категория**: Task Execution  
**Описание**: Запускает процесс выполнения задачи с загрузкой контекста и зависимостей

**Параметры**:
- `taskId` (string) - ID задачи для выполнения

**Пример использования**:
```
execute_task({ taskId: "task-123" })
```

**Поддерживаемые параметры промпта**:
- `{task}` - Детали задачи
- `{complexityAssessment}` - Оценка сложности
- `{relatedFilesSummary}` - Резюме связанных файлов
- `{dependencyTasks}` - Зависимые задачи
- `{potentialFiles}` - Потенциально связанные файлы

#### `verify_task`
**Назначение**: Проверка завершения задачи  
**Категория**: Task Execution  
**Описание**: Валидирует результаты выполнения задачи и выставляет оценку

**Параметры**:
- `taskId` (string) - ID задачи для проверки
- `score` (number) - Оценка от 0 до 100
- `summary` (string) - Резюме выполнения или предложения по исправлению

**Пример использования**:
```
verify_task({
  taskId: "task-123",
  score: 85,
  summary: "Задача выполнена успешно. API работает корректно, тесты проходят."
})
```

**Форматы резюме**:
- При score ≥ 80: `"Краткое описание результатов и важных решений"`
- При score < 80: `"Список проблем и предложения по исправлению"`

**Поддерживаемые параметры промпта**:
- `{task}` - Детали задачи

---

## 🔄 Диаграммы потоков

### Поток выполнения задачи
```
plan_task → analyze_task → reflect_task → split_tasks → execute_task → verify_task
```

### Поток управления задачами
```
list_tasks → query_task → get_task_detail → execute_task/delete_task
```

### Поток исследования
```
research_mode → process_thought → analyze_task → reflect_task
```

## 📊 Статусы задач

| Статус | Описание |
|--------|----------|
| `pending` | Задача создана, ожидает выполнения |
| `in_progress` | Задача в процессе выполнения |
| `completed` | Задача успешно завершена |
| `failed` | Задача завершена с ошибками |
| `blocked` | Задача заблокирована зависимостями |

## 🎯 Лучшие практики использования

### Планирование
1. Начинайте с `plan_task` для создания общего плана
2. Используйте `analyze_task` для детального анализа
3. Применяйте `reflect_task` для улучшения концепции

### Выполнение
1. Разбивайте сложные задачи с помощью `split_tasks`
2. Выполняйте задачи по одной с `execute_task`
3. Всегда проверяйте результаты с `verify_task`

### Мониторинг
1. Регулярно используйте `list_tasks` для обзора
2. Применяйте `query_task` для поиска конкретных задач
3. Используйте `get_task_detail` для детального анализа

### Исследование
1. Используйте `research_mode` для сложных технических вопросов
2. Применяйте `process_thought` для структурированного анализа
3. Документируйте результаты исследований
