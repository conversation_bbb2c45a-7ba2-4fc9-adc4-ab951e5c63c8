{"AgentConfig": {"tool_call_timeout": 45, "max_steps": 20, "request_limit": 2000, "total_tokens_limit": 150000}, "LLM": {"provider": "ollama", "model": "llama3.2:1b", "temperature": 0.3, "max_tokens": 8000, "max_context_length": 128000, "top_p": 0.95, "base_url": "http://localhost:11434"}, "mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "godly-filesystem": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/filesystem-manager/index.js"]}}}