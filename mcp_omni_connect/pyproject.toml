[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mcpomni_connect"
version = "0.1.15"
description = "Universal MCP Client with multi-transport support and LLM-powered tool routing"
readme = "README.md"
authors = [{ name = "Abiola Adeshina", email = "<EMAIL>" }]
requires-python = ">=3.10"

keywords = ["git", "mcp", "llm", "automation"]
license = { text = "MIT" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "colorlog>=6.9.0",
    "openai>=1.67.0",
    "python-dotenv>=1.0.1",
    "rich>=13.9.4",
    "websockets>=15.0.1",
    "httpx>=0.26.0",
    "httpx-sse>=0.4.0",
    "pydantic>=2.6.0",
    "anyio>=4.2.0",
    "groq>=0.20.0",
    "redis>=5.2.1",
    "python-decouple>=3.8",
    "mcp[cli]>=1.6.0",
    "deepseek>=1.0.0",
    "fastapi>=0.115.12",
    "python-multipart>=0.0.20",
    "ollama>=0.4.8",
   
]

[project.scripts]
mcpomni_connect = "mcpomni_connect.main:main"


[project.urls]
Repository = "https://github.com/Abiorh001/mcp_omni_connect"
Issues = "https://github.com/Abiorh001/mcp_omni_connect/issues"

[tool.hatch.build.targets.wheel]
packages = ["src/mcpomni_connect"]

[tool.hatch.build]
packages = ["src/mcpomni_connect"]
include = [

    "LICENSE",
    "README.md"
]

[tool.pytest.ini_options]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "asyncio: marks tests that use asyncio",
    "OpenAIIntegration: marks tests that require an OpenAI API key",
]

[tool.uv.sources]
mcpomni_connect = { workspace = true }

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "ruff>=0.11.7",
]
