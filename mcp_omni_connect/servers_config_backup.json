{"AgentConfig": {"tool_call_timeout": 45, "max_steps": 20, "request_limit": 2000, "total_tokens_limit": 150000}, "LLM": {"provider": "ollama", "model": "llama3.2:1b", "temperature": 0.3, "max_tokens": 8000, "max_context_length": 128000, "top_p": 0.95, "base_url": "http://localhost:11434"}, "mcpServers": {"mcp-installer": {"command": "npx", "args": ["@anaisbetts/mcp-installer"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "desktop-commander": {"command": "npx", "args": ["@wonderwhy-er/desktop-commander@latest"]}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["/Users/<USER>/Projects/handmade/node-native/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "/Volumes/SAB500/VideoLingo", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "ika-compressor-mcp": {"command": "npx", "args": ["-y", "/Users/<USER>/Projects/handmade/python-original/ika-compressor-mcp"], "env": {"PYTHON_PATH": "python3"}}, "godly-filesystem": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/filesystem-manager/index.js"]}, "godly-ssl": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/ssl-manager/index.js"]}, "godly-whatsapp": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/whatsapp-manager/index.js"], "env": {"TELEGRAM_BOT_TOKEN": "**********************************************"}}, "godly-timer": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-rewritten/timer-manager/index.js"], "env": {"TELEGRAM_BOT_TOKEN": "**********************************************"}}, "timer-mcp-server": {"command": "node", "args": ["/Users/<USER>/Projects/MCP_SERVS/timer-mcp-server/dist/index.js"]}, "videolingo-planner": {"command": "node", "args": ["/Users/<USER>/Projects/handmade/node-native/videolingo-planner/index.js"]}, "git-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/git", "run", "python", "-m", "mcp_server_git"]}, "fetch-server": {"command": "/usr/local/bin/uv", "args": ["--directory", "/Users/<USER>/Projects/MCP_SERVS/servers/src/fetch", "run", "python", "-m", "mcp_server_fetch"]}}}