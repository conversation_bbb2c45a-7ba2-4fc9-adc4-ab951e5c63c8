<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCPOmni-connect</title>
    <style>
        :root {
            --primary: #7289da;
            --background: #0a0a0a;
            --surface: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --gradient-start: #8a2be2;
            --gradient-end: #4a00e0;
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: var(--background);
            color: var(--text-primary);
            margin-top: 32px;
        }

        .app-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .app-logo {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: -0.5px;
            margin-bottom: 0.5rem;
        }

        .app-tagline {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 400;
        }

        .chat-container {
            background-color: var(--surface);
            border-radius: 1.5rem;
            box-shadow: 0 0 30px rgba(114, 137, 218, 0.1);
            padding: 1.5rem;
            height: 70vh;
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .messages {
            flex-grow: 1;
            overflow-y: auto;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 0.75rem;
            background: linear-gradient(145deg, rgba(40,40,40,0.2), rgba(25,25,25,0.1));
            scrollbar-width: thin;
            scrollbar-color: var(--primary) transparent;
        }

        .messages::-webkit-scrollbar {
            width: 6px;
        }

        .messages::-webkit-scrollbar-thumb {
            background-color: var(--primary);
            border-radius: 3px;
        }

        .message {
            margin-bottom: 1.25rem;
            padding: 1rem 1.25rem;
            border-radius: 1.25rem;
            max-width: 85%;
            position: relative;
            transition: transform 0.2s ease;
            line-height: 1.5;
        }

        .message:hover {
            transform: translateX(5px);
        }

        .user-message {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
            box-shadow: 0 4px 15px rgba(114, 137, 218, 0.3);
        }

        .assistant-message {
            background: rgba(50, 50, 50, 0.6);
            backdrop-filter: blur(10px);
            margin-right: auto;
            border-bottom-left-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-container {
            display: flex;
            gap: 1rem;
            padding: 0.5rem;
            background: rgba(40, 40, 40, 0.6);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        input[type="text"] {
            flex-grow: 1;
            padding: 1rem;
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1rem;
            border-radius: 0.75rem;
        }

        input[type="text"]:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--primary);
        }

        input[type="text"]::placeholder {
            color: var(--text-secondary);
        }

        button {
            padding: 0.8rem 1.5rem;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            border: none;
            border-radius: 0.75rem;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 20px rgba(114, 137, 218, 0.4);
        }

        button:active {
            transform: scale(0.98);
        }

        .timestamp {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }

        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .loading-dots {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            background: var(--text-primary);
            border-radius: 50%;
            animation: pulse 1.4s infinite;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
            padding: 0.5rem;
            margin-top: 0.5rem;
        }

        .status-indicator .dot {
            width: 8px;
            height: 8px;
            background-color: #4CAF50;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
        }

        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .message-content h3 {
            color: var(--primary);
            margin: 1rem 0 0.5rem 0;
            font-size: 1.1rem;
        }

        .message-content ul {
            margin: 0.5rem 0;
            padding-left: 1.2rem;
        }

        .message-content li {
            margin: 0.8rem 0;
        }

        .message-content strong {
            color: var(--gradient-start);
            font-weight: 600;
        }

        .message-content a {
            color: var(--primary);
            text-decoration: none;
            border-bottom: 1px dashed var(--primary);
        }

        .message-content a:hover {
            border-bottom-style: solid;
        }

        .price-highlight {
            font-size: 1.2rem;
            color: var(--gradient-start);
            font-weight: bold;
            display: block;
            margin: 1rem 0;
            padding: 0.5rem;
            border-radius: 0.5rem;
            background: rgba(138, 43, 226, 0.1);
            text-align: center;
        }

        .news-item {
            border-left: 2px solid var(--gradient-start);
            padding-left: 1rem;
            margin: 1rem 0;
        }

        .read-more {
            font-size: 0.8rem;
            color: var(--text-secondary);
            display: block;
            margin-top: 0.3rem;
        }

        .titlebar {
            -webkit-app-region: drag;
            height: 32px;
            background: var(--surface);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .titlebar-buttons {
            -webkit-app-region: no-drag;
            display: flex;
            gap: 0.5rem;
        }

        .titlebar-button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            padding: 0;
            margin: 0;
            cursor: pointer;
        }

        .titlebar-button.close { background: #ff5f56; }
        .titlebar-button.minimize { background: #ffbd2e; }
        .titlebar-button.maximize { background: #27c93f; }

        .titlebar-button:hover {
            filter: brightness(0.9);
        }

        .offline-indicator {
            position: fixed;
            top: 40px;
            right: 20px;
            background: #ff5f56;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            display: none;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="titlebar">
        <div class="titlebar-brand">MCPOmni Connect</div>
        <div class="titlebar-buttons">
            <button class="titlebar-button minimize"></button>
            <button class="titlebar-button maximize"></button>
            <button class="titlebar-button close"></button>
        </div>
    </div>

    <div class="offline-indicator">
        You are offline. Reconnecting...
    </div>

    <div class="app-header">
        <div class="app-logo">MCPOmni-connect</div>
        <div class="app-tagline">Seamless communication across all mcp servers</div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages"></div>
        <div class="input-container">
            <input type="text" id="userInput" placeholder="Ask me anything...">
            <button onclick="sendMessage()">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
                Send
            </button>
        </div>
    </div>

    <div class="status-indicator">
        <div class="dot"></div>
        <span>Connected to MCPOmni-connect server</span>
    </div>

    <script>
        const messagesContainer = document.getElementById('messages');
        const userInput = document.getElementById('userInput');
        const { ipcRenderer } = require('electron');

        // Add welcome message
        window.onload = function() {
            setTimeout(() => {
                addMessage('assistant', 'Welcome to MCPOmni-connect! How can I assist you today?');
            }, 500);
        };

        function createLoadingDots() {
            const container = document.createElement('div');
            container.className = 'loading-dots';
            container.innerHTML = `
                <span></span>
                <span></span>
                <span></span>
            `;
            return container;
        }

        function formatMessage(content) {
            // Format price
            content = content.replace(/(\$?\d+\.\d+)\s*dollars/, '<div class="price-highlight">$1 USD</div>');

            // Format headers
            content = content.replace(/###\s*(.*)/g, '<h3>$1</h3>');

            // Format bold text
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // Format links
            content = content.replace(/\[([^\]]+)\]\(([^\)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

            // Format numbered list items
            content = content.replace(/(\d+)\.\s+\*\*(.*?)\*\*:(.*?)(?=\d+\.|$)/g,
                '<div class="news-item"><strong>$2</strong>$3</div>');

            return content;
        }

        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', `${role}-message`);

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = formatMessage(content);

            const timestamp = document.createElement('div');
            timestamp.className = 'timestamp';
            timestamp.textContent = new Date().toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timestamp);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        function generateChatId() {
            return crypto.randomUUID(); // Use UUID for unique chat ID
        }
        async function sendMessage() {
            const query = userInput.value.trim();
            if (!query) return;

            addMessage('user', query);
            userInput.value = '';

            const loading = createLoadingDots();
            messagesContainer.appendChild(loading);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            const chatId = generateChatId();

            try {
                const response = await fetch('http://localhost:8000/chat/agent_chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                            query: query,
                            chat_id: "cc1c9dbe-4344-4511-b98b-f563d2526d6a"
                        })
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const text = decoder.decode(value);
                    const messages = text.split('\n').filter(msg => msg.trim());

                    for (const msg of messages) {
                        try {
                            const parsed = JSON.parse(msg);
                            messagesContainer.removeChild(loading);
                            addMessage(parsed.role, parsed.content);
                        } catch (e) {
                            console.error('Error parsing message:', e);
                        }
                    }
                }
            } catch (error) {
                messagesContainer.removeChild(loading);
                addMessage('assistant', '⚠️ Error connecting to the server. Please try again.');
                console.error('Error:', error);
            }
        }

        // Handle Enter key
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Window control buttons
        document.querySelector('.titlebar-button.close').addEventListener('click', () => {
            ipcRenderer.send('close-window');
        });

        document.querySelector('.titlebar-button.minimize').addEventListener('click', () => {
            ipcRenderer.send('minimize-window');
        });

        document.querySelector('.titlebar-button.maximize').addEventListener('click', () => {
            ipcRenderer.send('maximize-window');
        });

        // Offline detection
        const offlineIndicator = document.querySelector('.offline-indicator');

        window.addEventListener('online', () => {
            offlineIndicator.style.display = 'none';
        });

        window.addEventListener('offline', () => {
            offlineIndicator.style.display = 'block';
        });

        // Add auto-reconnect logic
        let reconnectInterval;

        function checkConnection() {
            if (!navigator.onLine) {
                offlineIndicator.style.display = 'block';
                reconnectInterval = setInterval(tryReconnect, 5000);
            }
        }

        async function tryReconnect() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    offlineIndicator.style.display = 'none';
                    clearInterval(reconnectInterval);
                }
            } catch (error) {
                console.log('Reconnection attempt failed');
            }
        }
    </script>
</body>
</html>
