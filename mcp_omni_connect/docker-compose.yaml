version: '3.8'

services:
  mcpomni_connect_redis:
    container_name: mcpomni_connect_redis
    image: redis:7.4.2-bookworm
    ports:
      - "6379:6379"
    command: ["redis-server", "--appendonly", "yes"]
    volumes:
      - mcpomni_connect_redis_data:/data
    restart: unless-stopped

  mcpomni_connect_qdrant:
    container_name: mcpomni_connect_qdrant
    image: qdrant/qdrant
    ports:
      - "6333:6333"
    volumes:
      - mcpomni_connect_qdrant_data:/qdrant/storage
    restart: unless-stopped
  # chroma_db:
  #     image: chromadb/chroma
  #     container_name: chroma_vector_db
  #     ports:
  #       - "8000:8000"
  #     volumes:
  #       - ./chroma_db:/chroma/chroma
  #     restart: unless-stopped

volumes:
  mcpomni_connect_redis_data:
  mcpomni_connect_qdrant_data:
