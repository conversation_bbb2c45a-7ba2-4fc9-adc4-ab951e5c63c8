# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
        exclude: ^.*demographic\.000000$
      - id: check-yaml
      - id: check-toml
      - id: check-added-large-files
        args: ["--maxkb", "500"]
        exclude: ^.*yarn-.*cjs$
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.7  # Specify the latest Ruff version
    hooks:
      - id: ruff
        types: [python]
        args: [--fix, --exit-non-zero-on-fix, --line-length=88]

      - id: ruff-format
        types: [python]

  - repo: https://github.com/astral-sh/uv-pre-commit
    # uv version.
    rev: 0.6.13
    hooks:
      - id: uv-lock
      - id: uv-export
