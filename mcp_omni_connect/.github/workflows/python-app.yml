# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Python application

on:
  push:
    branches: [ "main", "dev" ]
  pull_request:
    branches: [ "main", "dev" ]

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.12
      uses: actions/setup-python@v5
      with:
        python-version: "3.12"

    - name: Install uv
      run: |
        python -m pip install --upgrade pip
        pip install uv

    - name: Check if lock file is in sync
      run: |
        if ! uv lock --check; then
          echo "::error::uv.lock is out of sync with pyproject.toml. Please run 'uv lock' locally and commit the updated lock file."
          exit 1
        fi

    - name: Set up virtual environment
      run: |
        uv venv .venv
        echo "${{ github.workspace }}/.venv/bin" >> $GITHUB_PATH

    - name: Cache virtual environment
      uses: actions/cache@v4
      id: venv-cache
      with:
        path: .venv
        key: venv-${{ hashFiles('**/pyproject.toml') }}

    - name: Ensure cache is healthy
      if: steps.venv-cache.outputs.cache-hit == 'true'
      shell: bash
      run: |
        timeout 10s .venv/bin/python -m pip --version || rm -rf .venv

    - name: Install dependencies
      run: |
        uv sync --group dev

    - name: Lint with ruff
      run: |
        ruff check

    - name: Run unit tests with pytest
      run: |
        cp test.servers_config.json servers_config.json
        uv run pytest -v tests

    # - name: Run integration tests with OpenAIIntegration
    #   run: |
    #     uv run pytest -m "OpenAIIntegration" -v
