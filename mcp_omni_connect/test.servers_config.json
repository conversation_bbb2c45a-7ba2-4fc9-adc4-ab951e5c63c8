{"AgentConfig": {"tool_call_timeout": 30, "max_steps": 15, "request_limit": 1000, "total_tokens_limit": 100000}, "LLM": {"provider": "lmstudio", "model": "deepseek-coder-v2-lite-instruct-mlx", "temperature": 0.5, "max_tokens": 5000, "max_context_length": 30000, "top_p": 0}, "mcpServers": {"filesystem-server": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/ai/MCPOmniConnect/mcp_omni_connect/playground"]}, "sse-server": {"type": "sse", "url": "http://localhost:3000/mcp", "headers": {"Authorization": "Bearer token"}}, "docker-server": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/server"]}}}