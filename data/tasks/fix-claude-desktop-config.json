{"id": "fix-claude-desktop-config-001", "title": "Исправление конфигурации <PERSON> серверов", "description": "Восстановление и оптимизация claude_desktop_config.json после случайной перезаписи", "status": "pending", "priority": "high", "created": "2024-12-23T03:45:00Z", "updated": "2024-12-23T03:45:00Z", "category": "configuration", "tags": ["mcp", "configuration", "claude-desktop", "servers", "bugfix"], "requirements": {"description": "Файл claude_desktop_config.json был случайно перезаписан и содержит только 2 сервера вместо полной конфигурации. Нужно восстановить все серверы из backup файла и исправить проблемы.", "constraints": ["Сохранить работоспособность всех существующих серверов", "Удалить дубликаты, отдав приоритет локальным серверам", "Исправить все серверы с ошибками (⚠️)", "Валидировать JSON структуру", "Протестировать работу ключевых серверов"], "acceptance_criteria": ["Все серверы из backup восстановлены", "Нет дубликатов серверов", "Все пути к локальным файлам корректны", "mcp-shrimp-task-manager настроен с новыми параметрами", "n8n-mcp-server добавлен и работает", "JSON файл валиден", "<PERSON>op успешно загружает все серверы"]}, "context": {"files": {"backup_file": "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config_backup.json", "target_file": "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json", "local_servers_dir": "/Users/<USER>/Projects/MCP_SERVS/", "new_servers_dir": "/Users/<USER>/Projects/MCP_TOOLS/"}, "current_issues": ["Много серверов показывают ошибки (⚠️) в интерфейсе Claude", "Дубликаты серверов (например, несколько youtube-analytics)", "Неправильные пути к локальным серверам", "Отсутствующие или неправильные команды запуска", "Конфигурация содержит только 2 сервера вместо ~20"], "servers_to_prioritize": ["mcp-shrimp-task-manager (локальный с новыми параметрами)", "n8n-mcp-server (новый локальный)", "ika-compressor-mcp (локальный)", "mcp-ssh-server (локальный)", "timer-mcp-server (локальный)", "browser-use-mcp-server (локальный)", "git-server (локальный)", "fetch-server (локальный)"]}, "plan": {"phases": [{"phase": 1, "title": "Анализ и подготовка", "tasks": ["Проанализировать содержимое backup файла", "Идентифицировать все серверы и их статус", "Составить список дубликатов", "Проверить существование локальных файлов серверов"]}, {"phase": 2, "title": "Очистка и оптимизация", "tasks": ["Удалить дубликаты серверов", "Исправить пути к локальным серверам", "Обновить команды запуска для проблемных серверов", "Добавить отсутствующие environment переменные"]}, {"phase": 3, "title": "Интеграция новых серверов", "tasks": ["Настроить mcp-shrimp-task-manager с кастомными промптами", "Добавить n8n-mcp-server с правильной конфигурацией", "Проверить совместимость всех серверов"]}, {"phase": 4, "title": "Валидация и тестирование", "tasks": ["Валидировать JSON структуру", "Создать backup текущей конфигурации", "Применить новую конфигурацию", "Протестировать загрузку в Claude Desktop", "Проверить работу ключевых серверов"]}]}, "subtasks": [{"id": "analyze-backup", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> файла", "description": "Проанализир<PERSON>ать claude_desktop_config_backup.json и составить список всех серверов", "status": "pending", "estimated_time": "15 минут"}, {"id": "identify-duplicates", "title": "Идентификация дубликатов", "description": "Найти и составить список дублирующихся серверов для удаления", "status": "pending", "estimated_time": "10 минут"}, {"id": "verify-local-paths", "title": "Проверка локальных путей", "description": "Проверить существование всех файлов локальных серверов", "status": "pending", "estimated_time": "20 минут"}, {"id": "fix-server-configs", "title": "Исправление конфигураций серверов", "description": "Исправить команды запуска и пути для проблемных серверов", "status": "pending", "estimated_time": "30 минут"}, {"id": "configure-shrimp-task-manager", "title": "Настройка Shrimp Task Manager", "description": "Настроить mcp-shrimp-task-manager с новыми параметрами и кастомными промптами", "status": "pending", "estimated_time": "15 минут"}, {"id": "add-n8n-server", "title": "Добавление n8n MCP сервера", "description": "Добавить и настроить n8n-mcp-server в конфигурацию", "status": "pending", "estimated_time": "10 минут"}, {"id": "create-final-config", "title": "Создание итоговой конфигурации", "description": "Собрать все исправления в финальный claude_desktop_config.json", "status": "pending", "estimated_time": "20 минут"}, {"id": "validate-and-test", "title": "Валидация и тестирование", "description": "Проверить JSON, применить конфигурацию и протестировать в Claude Desktop", "status": "pending", "estimated_time": "25 минут"}], "resources": {"documentation": ["/Users/<USER>/Projects/MCP_TOOLS/mcp-shrimp-task-manager-docs/", "https://modelcontextprotocol.io/docs"], "tools_needed": ["jq (для валидации JSON)", "текстовый редактор", "<PERSON> (для тестирования)"], "dependencies": ["Backup файл должен быть доступен", "Локальные серверы должны быть скомпилированы", "n8n должен быть запущен на localhost:5678"]}, "risk_assessment": {"high_risks": ["Потеря работающих серверов при неправильном восстановлении", "Конфликты между дублирующимися серверами"], "mitigation": ["Создание дополнительного backup перед изменениями", "Пошаговое тестирование каждого этапа", "Возможность быстрого отката к предыдущей конфигурации"]}, "success_metrics": {"primary": ["Все серверы загружаются без ошибок в Claude Desktop", "Shrimp Task Manager работает с новыми параметрами", "n8n MCP сервер успешно подключается"], "secondary": ["Уменьшение количества серверов с ⚠️ до нуля", "Оптимизация времени загрузки <PERSON>", "Улучшение организации серверов"]}, "notes": ["Задача создана после случайной перезаписи конфигурации", "Приоритет отдается локальным серверам над внешними", "Необходимо сохранить все работающие интеграции", "После завершения обновить документацию"]}