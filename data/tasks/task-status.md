# 📋 Статус задачи: Исправление конфигурации Claude Desktop

## 🎯 Задача создана в Shrimp Task Manager

**ID задачи**: `fix-claude-desktop-config-001`  
**Статус**: `pending` → готова к выполнению  
**Приоритет**: `high`  
**Создана**: 2024-12-23T03:45:00Z

## 📁 Созданные файлы

### 1. Основная задача
**Файл**: `/Users/<USER>/Projects/MCP_TOOLS/data/tasks/fix-claude-desktop-config.json`
- ✅ Детальное описание проблемы
- ✅ Требования и критерии приемки
- ✅ Контекст и файлы для работы
- ✅ План из 4 фаз
- ✅ 8 подзадач с оценкой времени
- ✅ Оценка рисков и метрики успеха

### 2. Правила проекта
**Файл**: `/Users/<USER>/Projects/MCP_TOOLS/data/shrimp-rules.md`
- ✅ Стандарты конфигурации MCP серверов
- ✅ Процедуры тестирования
- ✅ Стратегия backup и восстановления
- ✅ Шаблоны конфигураций
- ✅ Критерии успеха

## 🎯 Структура задачи

### Фазы выполнения

#### Фаза 1: Анализ и подготовка
- [ ] Проанализировать содержимое backup файла
- [ ] Идентифицировать все серверы и их статус
- [ ] Составить список дубликатов
- [ ] Проверить существование локальных файлов серверов

#### Фаза 2: Очистка и оптимизация
- [ ] Удалить дубликаты серверов
- [ ] Исправить пути к локальным серверам
- [ ] Обновить команды запуска для проблемных серверов
- [ ] Добавить отсутствующие environment переменные

#### Фаза 3: Интеграция новых серверов
- [ ] Настроить mcp-shrimp-task-manager с кастомными промптами
- [ ] Добавить n8n-mcp-server с правильной конфигурацией
- [ ] Проверить совместимость всех серверов

#### Фаза 4: Валидация и тестирование
- [ ] Валидировать JSON структуру
- [ ] Создать backup текущей конфигурации
- [ ] Применить новую конфигурацию
- [ ] Протестировать загрузку в Claude Desktop
- [ ] Проверить работу ключевых серверов

## 📊 Подзадачи (8 штук)

| ID | Название | Статус | Время |
|----|----------|--------|-------|
| `analyze-backup` | Анализ backup файла | pending | 15 мин |
| `identify-duplicates` | Идентификация дубликатов | pending | 10 мин |
| `verify-local-paths` | Проверка локальных путей | pending | 20 мин |
| `fix-server-configs` | Исправление конфигураций | pending | 30 мин |
| `configure-shrimp-task-manager` | Настройка Shrimp Task Manager | pending | 15 мин |
| `add-n8n-server` | Добавление n8n MCP сервера | pending | 10 мин |
| `create-final-config` | Создание итоговой конфигурации | pending | 20 мин |
| `validate-and-test` | Валидация и тестирование | pending | 25 мин |

**Общее время**: ~145 минут (2.5 часа)

## 🎯 Ключевые файлы для работы

### Исходные файлы
- **Backup**: `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config_backup.json`
- **Текущий**: `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json`

### Директории серверов
- **Локальные**: `/Users/<USER>/Projects/MCP_SERVS/`
- **Новые**: `/Users/<USER>/Projects/MCP_TOOLS/`

### Приоритетные серверы
1. `mcp-shrimp-task-manager` (с новыми параметрами)
2. `n8n-mcp-server` (новый)
3. `ika-compressor-mcp` (локальный)
4. `mcp-ssh-server` (локальный)
5. `timer-mcp-server` (локальный)
6. `browser-use-mcp-server` (локальный)

## 🚨 Выявленные проблемы

### Из скриншотов интерфейса
- Много серверов с ⚠️ (ошибки загрузки)
- Дубликаты серверов (например, `youtube-analytics`)
- Неправильные пути к файлам
- Отсутствующие зависимости

### Текущее состояние
- `claude_desktop_config.json` содержит только 2 сервера
- Потеряны все остальные серверы из оригинальной конфигурации
- Нужно восстановление из backup

## 🎯 Критерии успеха

### Основные
- [ ] Все серверы загружаются без ошибок в Claude Desktop
- [ ] Shrimp Task Manager работает с новыми параметрами
- [ ] n8n MCP сервер успешно подключается
- [ ] Нет дубликатов серверов
- [ ] JSON конфигурация валидна

### Дополнительные
- [ ] Количество серверов с ⚠️ = 0
- [ ] Время загрузки Claude Desktop оптимизировано
- [ ] Все локальные серверы имеют приоритет
- [ ] Документация обновлена

## 🚀 Готовность к выполнению

### ✅ Готово
- Задача структурирована и детализирована
- Правила проекта определены
- Файлы для работы идентифицированы
- План выполнения составлен
- Критерии успеха установлены

### 🎯 Следующий шаг
**Начать выполнение Фазы 1**: Анализ backup файла и идентификация всех серверов

---

**Задача готова к выполнению! Можно приступать к реализации.** 🚀
