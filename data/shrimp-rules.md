# 🦐 Shrimp Task Manager - Правила проекта MCP конфигураций

## 🎯 Цель проекта

Исправление и оптимизация конфигураций MCP серверов для обеспечения стабильной работы всех клиентов (Claude Desktop, MCP SuperAssistant, Warp).

## 📋 Стандарты конфигурации MCP серверов

### 🏗️ Архитектурные принципы

1. **Приоритет локальных серверов**
   - Локальные серверы имеют приоритет над внешними
   - Путь: `/Users/<USER>/Projects/MCP_SERVS/` и `/Users/<USER>/Projects/MCP_TOOLS/`
   - Использовать прямые пути к скомпилированным файлам

2. **Избегание дубликатов**
   - Один сервер = одна запись в конфигурации
   - При дубликатах выбирать локальную версию
   - Удалять устаревшие или неработающие записи

3. **Валидация конфигурации**
   - Все JSON файлы должны быть валидными
   - Проверять существование файлов перед добавлением
   - Тестировать загрузку в Claude Desktop

### 💻 Стандарты именования

#### Серверы
```json
{
  "server-name": {
    "command": "node|npx|python|go|uv",
    "args": ["path/to/server"],
    "env": {}
  }
}
```

#### Локальные серверы (приоритет)
- `mcp-shrimp-task-manager` - управление задачами
- `n8n-mcp-server` - автоматизация n8n
- `ika-compressor-mcp` - сжатие файлов
- `mcp-ssh-server` - SSH подключения
- `timer-mcp-server` - управление временем

#### Внешние серверы (через npx)
- `memory` - память между сессиями
- `sequential-thinking` - последовательное мышление
- `filesystem` - файловая система
- `desktop-commander` - управление рабочим столом

### 🔧 Команды запуска

#### Node.js серверы
```json
{
  "command": "node",
  "args": ["/absolute/path/to/dist/index.js"]
}
```

#### NPX серверы
```json
{
  "command": "npx",
  "args": ["-y", "package-name"]
}
```

#### Python серверы
```json
{
  "command": "/usr/local/bin/uv",
  "args": [
    "--directory",
    "/path/to/server",
    "run",
    "python",
    "-m",
    "module_name"
  ]
}
```

#### Go серверы
```json
{
  "command": "go",
  "args": ["run", "main.go"],
  "cwd": "/path/to/server"
}
```

### 🌐 Environment переменные

#### Обязательные для Shrimp Task Manager
```json
{
  "env": {
    "DATA_DIR": "/Users/<USER>/Projects/MCP_TOOLS/data",
    "TEMPLATES_USE": "en",
    "ENABLE_GUI": "false",
    "MCP_PROMPT_PLAN_TASK_APPEND": "## MCP Tools Project Guidelines...",
    "MCP_PROMPT_EXECUTE_TASK_APPEND": "## MCP Development Standards..."
  }
}
```

#### Обязательные для n8n MCP Server
```json
{
  "env": {
    "N8N_BASE_URL": "http://localhost:5678",
    "N8N_API_KEY": "your-api-key"
  }
}
```

## 🧪 Процедуры тестирования

### 1. Валидация JSON
```bash
jq . config.json > /dev/null && echo "✅ Valid" || echo "❌ Invalid"
```

### 2. Проверка файлов
```bash
# Для каждого локального сервера
test -f "/path/to/server/file" && echo "✅ Exists" || echo "❌ Missing"
```

### 3. Тестирование в Claude Desktop
1. Сохранить backup текущей конфигурации
2. Применить новую конфигурацию
3. Перезапустить Claude Desktop
4. Проверить загрузку всех серверов
5. Протестировать ключевые команды

## 🔒 Безопасность

### Backup стратегия
- Всегда создавать backup перед изменениями
- Использовать временные метки в именах backup файлов
- Хранить минимум 3 последние версии

### Восстановление
```bash
# Быстрое восстановление
cp config_backup_YYYYMMDD_HHMMSS.json config.json
```

## 📊 Мониторинг

### Индикаторы проблем
- ⚠️ в интерфейсе Claude Desktop
- Медленная загрузка приложения
- Ошибки в логах
- Недоступность команд серверов

### Регулярные проверки
- Еженедельная проверка работоспособности всех серверов
- Ежемесячное обновление внешних серверов
- Квартальная оптимизация конфигурации

## 🚀 Процесс развертывания

### 1. Подготовка
- [ ] Создать backup текущей конфигурации
- [ ] Проверить доступность всех локальных серверов
- [ ] Валидировать новую конфигурацию

### 2. Применение
- [ ] Остановить Claude Desktop
- [ ] Применить новую конфигурацию
- [ ] Запустить Claude Desktop
- [ ] Проверить загрузку серверов

### 3. Тестирование
- [ ] Протестировать ключевые команды
- [ ] Проверить работу новых серверов
- [ ] Убедиться в отсутствии ошибок

### 4. Документирование
- [ ] Обновить документацию изменений
- [ ] Записать решенные проблемы
- [ ] Обновить список серверов

## 📝 Шаблоны конфигураций

### Минимальная конфигурация
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {}
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>"],
      "env": {}
    }
  }
}
```

### Полная конфигурация
- Все локальные серверы
- Основные внешние серверы
- Кастомные настройки для специфичных серверов

## 🎯 Критерии успеха

### Технические
- [ ] Все серверы загружаются без ошибок
- [ ] JSON конфигурация валидна
- [ ] Нет дубликатов серверов
- [ ] Время загрузки Claude Desktop < 10 секунд

### Функциональные
- [ ] Все команды серверов работают
- [ ] Shrimp Task Manager отвечает на запросы
- [ ] n8n интеграция функционирует
- [ ] Локальные серверы имеют приоритет

### Пользовательские
- [ ] Интерфейс Claude Desktop чистый (без ⚠️)
- [ ] Быстрый доступ к командам
- [ ] Стабильная работа всех функций
