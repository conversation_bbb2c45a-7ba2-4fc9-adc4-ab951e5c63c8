🚀 Setting up CUA playground environment...
🐍 Setting up Python environment...
📦 Updating CUA packages...
Requirement already satisfied: pip in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (23.2.1)
Collecting pip
  Obtaining dependency information for pip from https://files.pythonhosted.org/packages/29/a2/d40fb2460e883eca5199c62cfc2463fd261f760556ae6290f88488c362c0/pip-25.1.1-py3-none-any.whl.metadata
  Downloading pip-25.1.1-py3-none-any.whl.metadata (3.6 kB)
Requirement already satisfied: setuptools in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (65.5.0)
Collecting setuptools
  Obtaining dependency information for setuptools from https://files.pythonhosted.org/packages/58/29/93c53c098d301132196c3238c312825324740851d77a8500a2462c0fd888/setuptools-80.8.0-py3-none-any.whl.metadata
  Downloading setuptools-80.8.0-py3-none-any.whl.metadata (6.6 kB)
Collecting wheel
  Obtaining dependency information for wheel from https://files.pythonhosted.org/packages/0b/2c/87f3254fd8ffd29e4c02732eee68a83a1d3c346ae39bc6822dcbcb697f2b/wheel-0.45.1-py3-none-any.whl.metadata
  Downloading wheel-0.45.1-py3-none-any.whl.metadata (2.3 kB)
Collecting Cmake
  Obtaining dependency information for Cmake from https://files.pythonhosted.org/packages/d7/1f/2e86eb03ab8a52525347dede45ef3752b4516c19cc87be8a6546cef28839/cmake-4.0.2-py3-none-macosx_10_10_universal2.whl.metadata
  Downloading cmake-4.0.2-py3-none-macosx_10_10_universal2.whl.metadata (6.3 kB)
Downloading pip-25.1.1-py3-none-any.whl (1.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.8/1.8 MB 5.1 MB/s eta 0:00:00
Downloading setuptools-80.8.0-py3-none-any.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 42.0 MB/s eta 0:00:00
Downloading wheel-0.45.1-py3-none-any.whl (72 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 72.5/72.5 kB 10.9 MB/s eta 0:00:00
Downloading cmake-4.0.2-py3-none-macosx_10_10_universal2.whl (48.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 48.7/48.7 MB 38.8 MB/s eta 0:00:00
Installing collected packages: wheel, setuptools, pip, Cmake
  Attempting uninstall: setuptools
    Found existing installation: setuptools 65.5.0
    Uninstalling setuptools-65.5.0:
      Successfully uninstalled setuptools-65.5.0
  Attempting uninstall: pip
    Found existing installation: pip 23.2.1
    Uninstalling pip-23.2.1:
      Successfully uninstalled pip-23.2.1
Successfully installed Cmake-4.0.2 pip-25.1.1 setuptools-80.8.0 wheel-0.45.1
Collecting cua-computer
  Using cached cua_computer-0.2.2-py3-none-any.whl.metadata (5.8 kB)
Collecting cua-agent[all]
  Using cached cua_agent-0.2.1-py3-none-any.whl.metadata (12 kB)
Collecting pillow>=10.0.0 (from cua-computer)
  Downloading pillow-11.2.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (8.9 kB)
Collecting websocket-client>=1.8.0 (from cua-computer)
  Downloading websocket_client-1.8.0-py3-none-any.whl.metadata (8.0 kB)
Collecting websockets>=12.0 (from cua-computer)
  Downloading websockets-15.0.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting aiohttp>=3.9.0 (from cua-computer)
  Downloading aiohttp-3.11.18-cp311-cp311-macosx_11_0_arm64.whl.metadata (7.7 kB)
Collecting cua-core<0.2.0,>=0.1.0 (from cua-computer)
  Using cached cua_core-0.1.5-py3-none-any.whl.metadata (1.6 kB)
Collecting pydantic>=2.11.1 (from cua-computer)
  Downloading pydantic-2.11.5-py3-none-any.whl.metadata (67 kB)
Collecting httpx>=0.24.0 (from cua-core<0.2.0,>=0.1.0->cua-computer)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting posthog>=3.20.0 (from cua-core<0.2.0,>=0.1.0->cua-computer)
  Downloading posthog-4.1.0-py2.py3-none-any.whl.metadata (3.0 kB)
Collecting asyncio (from cua-agent[all])
  Using cached asyncio-3.4.3-py3-none-any.whl.metadata (1.7 kB)
Collecting anyio<5.0.0,>=4.4.1 (from cua-agent[all])
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting typing-extensions<5.0.0,>=4.12.2 (from cua-agent[all])
  Using cached typing_extensions-4.13.2-py3-none-any.whl.metadata (3.0 kB)
Collecting rich<14.0.0,>=13.7.1 (from cua-agent[all])
  Downloading rich-13.9.4-py3-none-any.whl.metadata (18 kB)
Collecting python-dotenv<2.0.0,>=1.0.1 (from cua-agent[all])
  Using cached python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)
Collecting cua-computer
  Using cached cua_computer-0.1.29-py3-none-any.whl.metadata (5.6 kB)
Collecting certifi>=2024.2.2 (from cua-agent[all])
  Downloading certifi-2025.4.26-py3-none-any.whl.metadata (2.5 kB)
Collecting torch>=2.2.1 (from cua-agent[all])
  Downloading torch-2.7.0-cp311-none-macosx_11_0_arm64.whl.metadata (29 kB)
Collecting torchvision>=0.17.1 (from cua-agent[all])
  Downloading torchvision-0.22.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.1 kB)
Collecting ultralytics>=8.0.0 (from cua-agent[all])
  Downloading ultralytics-8.3.143-py3-none-any.whl.metadata (37 kB)
Collecting transformers>=4.38.2 (from cua-agent[all])
  Downloading transformers-4.52.3-py3-none-any.whl.metadata (40 kB)
Collecting cua-som<0.2.0,>=0.1.0 (from cua-agent[all])
  Using cached cua_som-0.1.3-py3-none-any.whl.metadata (7.0 kB)
Collecting anthropic<0.47.0,>=0.46.0 (from cua-agent[all])
  Using cached anthropic-0.46.0-py3-none-any.whl.metadata (23 kB)
Collecting boto3<2.0.0,>=1.35.81 (from cua-agent[all])
  Downloading boto3-1.38.22-py3-none-any.whl.metadata (6.6 kB)
Collecting openai<2.0.0,>=1.14.0 (from cua-agent[all])
  Downloading openai-1.82.0-py3-none-any.whl.metadata (25 kB)
Collecting groq<0.5.0,>=0.4.0 (from cua-agent[all])
  Using cached groq-0.4.2-py3-none-any.whl.metadata (12 kB)
Collecting dashscope<2.0.0,>=1.13.0 (from cua-agent[all])
  Using cached dashscope-1.23.3-py3-none-any.whl.metadata (6.8 kB)
Collecting requests<3.0.0,>=2.31.0 (from cua-agent[all])
  Downloading requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)
Collecting ollama<0.5.0,>=0.4.7 (from cua-agent[all])
  Downloading ollama-0.4.8-py3-none-any.whl.metadata (4.7 kB)
Collecting gradio<6.0.0,>=5.23.3 (from cua-agent[all])
  Downloading gradio-5.31.0-py3-none-any.whl.metadata (16 kB)
Collecting pylume>=0.1.8 (from cua-computer)
  Using cached pylume-0.2.1-py3-none-any.whl.metadata (2.9 kB)
Collecting aiohappyeyeballs>=2.3.0 (from aiohttp>=3.9.0->cua-computer)
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp>=3.9.0->cua-computer)
  Using cached aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting attrs>=17.3.0 (from aiohttp>=3.9.0->cua-computer)
  Using cached attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp>=3.9.0->cua-computer)
  Downloading frozenlist-1.6.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (16 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp>=3.9.0->cua-computer)
  Downloading multidict-6.4.4-cp311-cp311-macosx_11_0_arm64.whl.metadata (5.3 kB)
Collecting propcache>=0.2.0 (from aiohttp>=3.9.0->cua-computer)
  Downloading propcache-0.3.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (10 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp>=3.9.0->cua-computer)
  Downloading yarl-1.20.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (72 kB)
Collecting distro<2,>=1.7.0 (from anthropic<0.47.0,>=0.46.0->cua-agent[all])
  Using cached distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting jiter<1,>=0.4.0 (from anthropic<0.47.0,>=0.46.0->cua-agent[all])
  Downloading jiter-0.10.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (5.2 kB)
Collecting sniffio (from anthropic<0.47.0,>=0.46.0->cua-agent[all])
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting idna>=2.8 (from anyio<5.0.0,>=4.4.1->cua-agent[all])
  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting botocore<1.39.0,>=1.38.22 (from boto3<2.0.0,>=1.35.81->cua-agent[all])
  Downloading botocore-1.38.22-py3-none-any.whl.metadata (5.7 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from boto3<2.0.0,>=1.35.81->cua-agent[all])
  Using cached jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting s3transfer<0.14.0,>=0.13.0 (from boto3<2.0.0,>=1.35.81->cua-agent[all])
  Downloading s3transfer-0.13.0-py3-none-any.whl.metadata (1.7 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botocore<1.39.0,>=1.38.22->boto3<2.0.0,>=1.35.81->cua-agent[all])
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting urllib3!=2.2.0,<3,>=1.25.4 (from botocore<1.39.0,>=1.38.22->boto3<2.0.0,>=1.35.81->cua-agent[all])
  Downloading urllib3-2.4.0-py3-none-any.whl.metadata (6.5 kB)
Collecting easyocr>=1.7.1 (from cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached easyocr-1.7.2-py3-none-any.whl.metadata (10 kB)
Collecting numpy>=1.26.4 (from cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading numpy-2.2.6-cp311-cp311-macosx_14_0_arm64.whl.metadata (62 kB)
Requirement already satisfied: setuptools>=75.8.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from cua-som<0.2.0,>=0.1.0->cua-agent[all]) (80.8.0)
Collecting opencv-python-headless>=********* (from cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached opencv_python_headless-*********-cp37-abi3-macosx_13_0_arm64.whl.metadata (20 kB)
Collecting matplotlib>=3.8.3 (from cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading matplotlib-3.10.3-cp311-cp311-macosx_11_0_arm64.whl.metadata (11 kB)
Collecting huggingface-hub>=0.21.4 (from cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading huggingface_hub-0.32.0-py3-none-any.whl.metadata (14 kB)
Collecting supervision>=0.25.1 (from cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached supervision-0.25.1-py3-none-any.whl.metadata (14 kB)
Collecting aiofiles<25.0,>=22.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Collecting fastapi<1.0,>=0.115.2 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)
Collecting ffmpy (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading ffmpy-0.5.0-py3-none-any.whl.metadata (3.0 kB)
Collecting gradio-client==1.10.1 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading gradio_client-1.10.1-py3-none-any.whl.metadata (7.1 kB)
Collecting groovy~=0.1 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached groovy-0.1.2-py3-none-any.whl.metadata (6.1 kB)
Collecting jinja2<4.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting markupsafe<4.0,>=2.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (4.0 kB)
Collecting orjson~=3.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading orjson-3.10.18-cp311-cp311-macosx_15_0_arm64.whl.metadata (41 kB)
Collecting packaging (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting pandas<3.0,>=1.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading pandas-2.2.3-cp311-cp311-macosx_11_0_arm64.whl.metadata (89 kB)
Collecting pydub (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting python-multipart>=0.0.18 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)
Collecting pyyaml<7.0,>=5.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (2.1 kB)
Collecting ruff>=0.9.3 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading ruff-0.11.11-py3-none-macosx_11_0_arm64.whl.metadata (25 kB)
Collecting safehttpx<0.2.0,>=0.1.6 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)
Collecting semantic-version~=2.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)
Collecting starlette<1.0,>=0.40.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)
Collecting tomlkit<0.14.0,>=0.12.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached tomlkit-0.13.2-py3-none-any.whl.metadata (2.7 kB)
Collecting typer<1.0,>=0.12 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached typer-0.15.4-py3-none-any.whl.metadata (15 kB)
Collecting uvicorn>=0.14.0 (from gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading uvicorn-0.34.2-py3-none-any.whl.metadata (6.5 kB)
Collecting fsspec (from gradio-client==1.10.1->gradio<6.0.0,>=5.23.3->cua-agent[all])
  Downloading fsspec-2025.5.0-py3-none-any.whl.metadata (11 kB)
Collecting httpcore==1.* (from httpx>=0.24.0->cua-core<0.2.0,>=0.1.0->cua-computer)
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx>=0.24.0->cua-core<0.2.0,>=0.1.0->cua-computer)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting tqdm>4 (from openai<2.0.0,>=1.14.0->cua-agent[all])
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Collecting pytz>=2020.1 (from pandas<3.0,>=1.0->gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas<3.0,>=1.0->gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting annotated-types>=0.6.0 (from pydantic>=2.11.1->cua-computer)
  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic>=2.11.1->cua-computer)
  Downloading pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting typing-inspection>=0.4.0 (from pydantic>=2.11.1->cua-computer)
  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.1->botocore<1.39.0,>=1.38.22->boto3<2.0.0,>=1.35.81->cua-agent[all])
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting charset-normalizer<4,>=2 (from requests<3.0.0,>=2.31.0->cua-agent[all])
  Downloading charset_normalizer-3.4.2-cp311-cp311-macosx_10_9_universal2.whl.metadata (35 kB)
Collecting markdown-it-py>=2.2.0 (from rich<14.0.0,>=13.7.1->cua-agent[all])
  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich<14.0.0,>=13.7.1->cua-agent[all])
  Using cached pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)
Collecting click<8.2,>=8.0.0 (from typer<1.0,>=0.12->gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached click-8.1.8-py3-none-any.whl.metadata (2.3 kB)
Collecting shellingham>=1.3.0 (from typer<1.0,>=0.12->gradio<6.0.0,>=5.23.3->cua-agent[all])
  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)
Collecting scipy (from easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading scipy-1.15.3-cp311-cp311-macosx_14_0_arm64.whl.metadata (61 kB)
Collecting scikit-image (from easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading scikit_image-0.25.2-cp311-cp311-macosx_12_0_arm64.whl.metadata (14 kB)
Collecting python-bidi (from easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading python_bidi-0.6.6-cp311-cp311-macosx_11_0_arm64.whl.metadata (4.9 kB)
Collecting Shapely (from easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading shapely-2.1.1-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting pyclipper (from easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading pyclipper-1.3.0.post6-cp311-cp311-macosx_10_9_universal2.whl.metadata (9.0 kB)
Collecting ninja (from easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached ninja-********-py3-none-macosx_10_9_universal2.whl.metadata (5.0 kB)
Collecting filelock (from huggingface-hub>=0.21.4->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting hf-xet<2.0.0,>=1.1.2 (from huggingface-hub>=0.21.4->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading hf_xet-1.1.2-cp37-abi3-macosx_11_0_arm64.whl.metadata (879 bytes)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich<14.0.0,>=13.7.1->cua-agent[all])
  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting contourpy>=1.0.1 (from matplotlib>=3.8.3->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading contourpy-1.3.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (5.5 kB)
Collecting cycler>=0.10 (from matplotlib>=3.8.3->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)
Collecting fonttools>=4.22.0 (from matplotlib>=3.8.3->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading fonttools-4.58.0-cp311-cp311-macosx_10_9_universal2.whl.metadata (104 kB)
Collecting kiwisolver>=1.3.1 (from matplotlib>=3.8.3->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading kiwisolver-1.4.8-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.2 kB)
Collecting pyparsing>=2.3.1 (from matplotlib>=3.8.3->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Collecting backoff>=1.10.0 (from posthog>=3.20.0->cua-core<0.2.0,>=0.1.0->cua-computer)
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Collecting defusedxml<0.8.0,>=0.7.1 (from supervision>=0.25.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading defusedxml-0.7.1-py2.py3-none-any.whl.metadata (32 kB)
Collecting opencv-python>=4.5.5.64 (from supervision>=0.25.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached opencv_python-*********-cp37-abi3-macosx_13_0_arm64.whl.metadata (20 kB)
Collecting sympy>=1.13.3 (from torch>=2.2.1->cua-agent[all])
  Using cached sympy-1.14.0-py3-none-any.whl.metadata (12 kB)
Collecting networkx (from torch>=2.2.1->cua-agent[all])
  Downloading networkx-3.4.2-py3-none-any.whl.metadata (6.3 kB)
Collecting mpmath<1.4,>=1.1.0 (from sympy>=1.13.3->torch>=2.2.1->cua-agent[all])
  Downloading mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
Collecting regex!=2019.12.17 (from transformers>=4.38.2->cua-agent[all])
  Downloading regex-2024.11.6-cp311-cp311-macosx_11_0_arm64.whl.metadata (40 kB)
Collecting tokenizers<0.22,>=0.21 (from transformers>=4.38.2->cua-agent[all])
  Downloading tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting safetensors>=0.4.3 (from transformers>=4.38.2->cua-agent[all])
  Downloading safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl.metadata (3.8 kB)
Collecting psutil (from ultralytics>=8.0.0->cua-agent[all])
  Downloading psutil-7.0.0-cp36-abi3-macosx_11_0_arm64.whl.metadata (22 kB)
Collecting py-cpuinfo (from ultralytics>=8.0.0->cua-agent[all])
  Using cached py_cpuinfo-9.0.0-py3-none-any.whl.metadata (794 bytes)
Collecting ultralytics-thop>=2.0.0 (from ultralytics>=8.0.0->cua-agent[all])
  Using cached ultralytics_thop-2.0.14-py3-none-any.whl.metadata (9.4 kB)
Collecting imageio!=2.35.0,>=2.33 (from scikit-image->easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached imageio-2.37.0-py3-none-any.whl.metadata (5.2 kB)
Collecting tifffile>=2022.8.12 (from scikit-image->easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Downloading tifffile-2025.5.21-py3-none-any.whl.metadata (31 kB)
Collecting lazy-loader>=0.4 (from scikit-image->easyocr>=1.7.1->cua-som<0.2.0,>=0.1.0->cua-agent[all])
  Using cached lazy_loader-0.4-py3-none-any.whl.metadata (7.6 kB)
Using cached cua_core-0.1.5-py3-none-any.whl (11 kB)
Using cached cua_agent-0.2.1-py3-none-any.whl (151 kB)
Using cached cua_computer-0.1.29-py3-none-any.whl (39 kB)
Downloading aiohttp-3.11.18-cp311-cp311-macosx_11_0_arm64.whl (457 kB)
Using cached anthropic-0.46.0-py3-none-any.whl (223 kB)
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Downloading boto3-1.38.22-py3-none-any.whl (139 kB)
Downloading botocore-1.38.22-py3-none-any.whl (13.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.6/13.6 MB 32.2 MB/s eta 0:00:00
Using cached cua_som-0.1.3-py3-none-any.whl (20 kB)
Using cached dashscope-1.23.3-py3-none-any.whl (1.3 MB)
Using cached distro-1.9.0-py3-none-any.whl (20 kB)
Downloading gradio-5.31.0-py3-none-any.whl (54.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 54.2/54.2 MB 52.7 MB/s eta 0:00:00
Downloading gradio_client-1.10.1-py3-none-any.whl (323 kB)
Downloading aiofiles-24.1.0-py3-none-any.whl (15 kB)
Downloading fastapi-0.115.12-py3-none-any.whl (95 kB)
Downloading groovy-0.1.2-py3-none-any.whl (14 kB)
Using cached groq-0.4.2-py3-none-any.whl (65 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
Downloading jiter-0.10.0-cp311-cp311-macosx_11_0_arm64.whl (321 kB)
Using cached jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl (12 kB)
Downloading multidict-6.4.4-cp311-cp311-macosx_11_0_arm64.whl (37 kB)
Downloading numpy-2.2.6-cp311-cp311-macosx_14_0_arm64.whl (5.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.4/5.4 MB 45.5 MB/s eta 0:00:00
Downloading ollama-0.4.8-py3-none-any.whl (13 kB)
Downloading openai-1.82.0-py3-none-any.whl (720 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 720.4/720.4 kB 25.6 MB/s eta 0:00:00
Downloading orjson-3.10.18-cp311-cp311-macosx_15_0_arm64.whl (133 kB)
Downloading pandas-2.2.3-cp311-cp311-macosx_11_0_arm64.whl (11.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 11.3/11.3 MB 45.6 MB/s eta 0:00:00
Downloading pillow-11.2.1-cp311-cp311-macosx_11_0_arm64.whl (3.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.0/3.0 MB 41.9 MB/s eta 0:00:00
Downloading pydantic-2.11.5-py3-none-any.whl (444 kB)
Downloading pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl (1.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.9/1.9 MB 63.2 MB/s eta 0:00:00
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Downloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)
Downloading PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl (172 kB)
Downloading requests-2.32.3-py3-none-any.whl (64 kB)
Downloading charset_normalizer-3.4.2-cp311-cp311-macosx_10_9_universal2.whl (198 kB)
Downloading idna-3.10-py3-none-any.whl (70 kB)
Downloading rich-13.9.4-py3-none-any.whl (242 kB)
Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)
Downloading s3transfer-0.13.0-py3-none-any.whl (85 kB)
Downloading safehttpx-0.1.6-py3-none-any.whl (8.7 kB)
Downloading semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)
Downloading starlette-0.46.2-py3-none-any.whl (72 kB)
Downloading tomlkit-0.13.2-py3-none-any.whl (37 kB)
Using cached typer-0.15.4-py3-none-any.whl (45 kB)
Using cached click-8.1.8-py3-none-any.whl (98 kB)
Downloading typing_extensions-4.13.2-py3-none-any.whl (45 kB)
Downloading urllib3-2.4.0-py3-none-any.whl (128 kB)
Downloading websockets-15.0.1-cp311-cp311-macosx_11_0_arm64.whl (173 kB)
Downloading yarl-1.20.0-cp311-cp311-macosx_11_0_arm64.whl (94 kB)
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Using cached aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Using cached attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading certifi-2025.4.26-py3-none-any.whl (159 kB)
Using cached easyocr-1.7.2-py3-none-any.whl (2.9 MB)
Downloading frozenlist-1.6.0-cp311-cp311-macosx_11_0_arm64.whl (122 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Downloading huggingface_hub-0.32.0-py3-none-any.whl (509 kB)
Downloading hf_xet-1.1.2-cp37-abi3-macosx_11_0_arm64.whl (2.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.5/2.5 MB 40.2 MB/s eta 0:00:00
Downloading fsspec-2025.5.0-py3-none-any.whl (196 kB)
Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Downloading matplotlib-3.10.3-cp311-cp311-macosx_11_0_arm64.whl (8.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 8.1/8.1 MB 53.1 MB/s eta 0:00:00
Downloading contourpy-1.3.2-cp311-cp311-macosx_11_0_arm64.whl (254 kB)
Using cached cycler-0.12.1-py3-none-any.whl (8.3 kB)
Downloading fonttools-4.58.0-cp311-cp311-macosx_10_9_universal2.whl (2.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.8/2.8 MB 39.9 MB/s eta 0:00:00
Downloading kiwisolver-1.4.8-cp311-cp311-macosx_11_0_arm64.whl (65 kB)
Using cached opencv_python_headless-*********-cp37-abi3-macosx_13_0_arm64.whl (37.3 MB)
Downloading packaging-25.0-py3-none-any.whl (66 kB)
Downloading posthog-4.1.0-py2.py3-none-any.whl (93 kB)
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading propcache-0.3.1-cp311-cp311-macosx_11_0_arm64.whl (45 kB)
Using cached pylume-0.2.1-py3-none-any.whl (1.1 MB)
Using cached pyparsing-3.2.3-py3-none-any.whl (111 kB)
Downloading python_multipart-0.0.20-py3-none-any.whl (24 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Downloading ruff-0.11.11-py3-none-macosx_11_0_arm64.whl (10.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.4/10.4 MB 48.5 MB/s eta 0:00:00
Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Using cached supervision-0.25.1-py3-none-any.whl (181 kB)
Downloading defusedxml-0.7.1-py2.py3-none-any.whl (25 kB)
Downloading scipy-1.15.3-cp311-cp311-macosx_14_0_arm64.whl (22.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 22.4/22.4 MB 44.3 MB/s eta 0:00:00
Using cached opencv_python-*********-cp37-abi3-macosx_13_0_arm64.whl (37.3 MB)
Downloading torch-2.7.0-cp311-none-macosx_11_0_arm64.whl (68.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 68.6/68.6 MB 48.3 MB/s eta 0:00:00
Using cached sympy-1.14.0-py3-none-any.whl (6.3 MB)
Downloading mpmath-1.3.0-py3-none-any.whl (536 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 536.2/536.2 kB 18.6 MB/s eta 0:00:00
Downloading torchvision-0.22.0-cp311-cp311-macosx_11_0_arm64.whl (1.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.9/1.9 MB 29.0 MB/s eta 0:00:00
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
Downloading transformers-4.52.3-py3-none-any.whl (10.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.5/10.5 MB 51.0 MB/s eta 0:00:00
Downloading tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl (2.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.7/2.7 MB 39.5 MB/s eta 0:00:00
Downloading regex-2024.11.6-cp311-cp311-macosx_11_0_arm64.whl (284 kB)
Downloading safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl (418 kB)
Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Downloading ultralytics-8.3.143-py3-none-any.whl (1.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.0/1.0 MB 37.3 MB/s eta 0:00:00
Using cached ultralytics_thop-2.0.14-py3-none-any.whl (26 kB)
Downloading uvicorn-0.34.2-py3-none-any.whl (62 kB)
Downloading websocket_client-1.8.0-py3-none-any.whl (58 kB)
Using cached asyncio-3.4.3-py3-none-any.whl (101 kB)
Downloading ffmpy-0.5.0-py3-none-any.whl (6.0 kB)
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Downloading networkx-3.4.2-py3-none-any.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 41.9 MB/s eta 0:00:00
Using cached ninja-********-py3-none-macosx_10_9_universal2.whl (279 kB)
Downloading psutil-7.0.0-cp36-abi3-macosx_11_0_arm64.whl (239 kB)
Using cached py_cpuinfo-9.0.0-py3-none-any.whl (22 kB)
Downloading pyclipper-1.3.0.post6-cp311-cp311-macosx_10_9_universal2.whl (270 kB)
Downloading pydub-0.25.1-py2.py3-none-any.whl (32 kB)
Downloading python_bidi-0.6.6-cp311-cp311-macosx_11_0_arm64.whl (264 kB)
Downloading scikit_image-0.25.2-cp311-cp311-macosx_12_0_arm64.whl (13.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.2/13.2 MB 41.7 MB/s eta 0:00:00
Using cached imageio-2.37.0-py3-none-any.whl (315 kB)
Using cached lazy_loader-0.4-py3-none-any.whl (12 kB)
Downloading tifffile-2025.5.21-py3-none-any.whl (229 kB)
Downloading shapely-2.1.1-cp311-cp311-macosx_11_0_arm64.whl (1.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.6/1.6 MB 41.2 MB/s eta 0:00:00
Installing collected packages: pytz, python-bidi, pydub, pyclipper, py-cpuinfo, mpmath, asyncio, websockets, websocket-client, urllib3, tzdata, typing-extensions, tqdm, tomlkit, sympy, sniffio, six, shellingham, semantic-version, safetensors, ruff, regex, pyyaml, python-multipart, python-dotenv, pyparsing, pygments, psutil, propcache, pillow, packaging, orjson, numpy, ninja, networkx, multidict, mdurl, markupsafe, kiwisolver, jmespath, jiter, idna, hf-xet, h11, groovy, fsspec, frozenlist, fonttools, filelock, ffmpy, distro, defusedxml, cycler, click, charset-normalizer, certifi, backoff, attrs, annotated-types, aiohappyeyeballs, aiofiles, yarl, uvicorn, typing-inspection, tifffile, Shapely, scipy, requests, python-dateutil, pydantic-core, opencv-python-headless, opencv-python, markdown-it-py, lazy-loader, jinja2, imageio, httpcore, contourpy, anyio, aiosignal, torch, starlette, scikit-image, rich, pydantic, posthog, pandas, matplotlib, huggingface-hub, httpx, botocore, aiohttp, ultralytics-thop, typer, torchvision, tokenizers, supervision, safehttpx, s3transfer, pylume, openai, ollama, groq, gradio-client, fastapi, dashscope, cua-core, anthropic, ultralytics, transformers, gradio, easyocr, cua-computer, boto3, cua-som, cua-agent

Successfully installed Shapely-2.1.1 aiofiles-24.1.0 aiohappyeyeballs-2.6.1 aiohttp-3.11.18 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.46.0 anyio-4.9.0 asyncio-3.4.3 attrs-25.3.0 backoff-2.2.1 boto3-1.38.22 botocore-1.38.22 certifi-2025.4.26 charset-normalizer-3.4.2 click-8.1.8 contourpy-1.3.2 cua-agent-0.2.1 cua-computer-0.1.29 cua-core-0.1.5 cua-som-0.1.3 cycler-0.12.1 dashscope-1.23.3 defusedxml-0.7.1 distro-1.9.0 easyocr-1.7.2 fastapi-0.115.12 ffmpy-0.5.0 filelock-3.18.0 fonttools-4.58.0 frozenlist-1.6.0 fsspec-2025.5.0 gradio-5.31.0 gradio-client-1.10.1 groovy-0.1.2 groq-0.4.2 h11-0.16.0 hf-xet-1.1.2 httpcore-1.0.9 httpx-0.28.1 huggingface-hub-0.32.0 idna-3.10 imageio-2.37.0 jinja2-3.1.6 jiter-0.10.0 jmespath-1.0.1 kiwisolver-1.4.8 lazy-loader-0.4 markdown-it-py-3.0.0 markupsafe-3.0.2 matplotlib-3.10.3 mdurl-0.1.2 mpmath-1.3.0 multidict-6.4.4 networkx-3.4.2 ninja-******** numpy-2.2.6 ollama-0.4.8 openai-1.82.0 opencv-python-********* opencv-python-headless-********* orjson-3.10.18 packaging-25.0 pandas-2.2.3 pillow-11.2.1 posthog-4.1.0 propcache-0.3.1 psutil-7.0.0 py-cpuinfo-9.0.0 pyclipper-1.3.0.post6 pydantic-2.11.5 pydantic-core-2.33.2 pydub-0.25.1 pygments-2.19.1 pylume-0.2.1 pyparsing-3.2.3 python-bidi-0.6.6 python-dateutil-2.9.0.post0 python-dotenv-1.1.0 python-multipart-0.0.20 pytz-2025.2 pyyaml-6.0.2 regex-2024.11.6 requests-2.32.3 rich-13.9.4 ruff-0.11.11 s3transfer-0.13.0 safehttpx-0.1.6 safetensors-0.5.3 scikit-image-0.25.2 scipy-1.15.3 semantic-version-2.10.0 shellingham-1.5.4 six-1.17.0 sniffio-1.3.1 starlette-0.46.2 supervision-0.25.1 sympy-1.14.0 tifffile-2025.5.21 tokenizers-0.21.1 tomlkit-0.13.2 torch-2.7.0 torchvision-0.22.0 tqdm-4.67.1 transformers-4.52.3 typer-0.15.4 typing-extensions-4.13.2 typing-inspection-0.4.1 tzdata-2025.2 ultralytics-8.3.143 ultralytics-thop-2.0.14 urllib3-2.4.0 uvicorn-0.34.2 websocket-client-1.8.0 websockets-15.0.1 yarl-1.20.0
Collecting git+https://github.com/ddupont808/mlx-vlm.git@stable/fix/qwen2-position-id
  Cloning https://github.com/ddupont808/mlx-vlm.git (to revision stable/fix/qwen2-position-id) to /private/var/folders/1l/tn__n8td7bbf07qcrtzgh8p40000gn/T/pip-req-build-on7anxcb
  Running command git clone --filter=blob:none --quiet https://github.com/ddupont808/mlx-vlm.git /private/var/folders/1l/tn__n8td7bbf07qcrtzgh8p40000gn/T/pip-req-build-on7anxcb
  Running command git checkout -b stable/fix/qwen2-position-id --track origin/stable/fix/qwen2-position-id
  Switched to a new branch 'stable/fix/qwen2-position-id'
  branch 'stable/fix/qwen2-position-id' set up to track 'origin/stable/fix/qwen2-position-id'.
  Resolved https://github.com/ddupont808/mlx-vlm.git to commit dd0e0b3e9f2a3bee0af0ebbf86d2ce4ab46beea8
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting mlx>=0.25.0 (from mlx-vlm==0.1.25)
  Downloading mlx-0.25.2-cp311-cp311-macosx_15_0_arm64.whl.metadata (5.3 kB)
Collecting datasets>=2.19.1 (from mlx-vlm==0.1.25)
  Using cached datasets-3.6.0-py3-none-any.whl.metadata (19 kB)
Requirement already satisfied: tqdm>=4.66.2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (4.67.1)
Requirement already satisfied: numpy>=1.23.4 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (2.2.6)
Requirement already satisfied: transformers>=4.51.3 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (4.52.3)
Requirement already satisfied: gradio>=5.19.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (5.31.0)
Requirement already satisfied: Pillow>=10.3.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (11.2.1)
Requirement already satisfied: requests>=2.31.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (2.32.3)
Collecting opencv-python==********* (from mlx-vlm==0.1.25)
  Downloading opencv_python-*********-cp37-abi3-macosx_11_0_arm64.whl.metadata (20 kB)
Collecting mlx-lm>=0.23.0 (from mlx-vlm==0.1.25)
  Downloading mlx_lm-0.24.1-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: fastapi>=0.95.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from mlx-vlm==0.1.25) (0.115.12)
Requirement already satisfied: filelock in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from datasets>=2.19.1->mlx-vlm==0.1.25) (3.18.0)
Collecting pyarrow>=15.0.0 (from datasets>=2.19.1->mlx-vlm==0.1.25)
  Downloading pyarrow-20.0.0-cp311-cp311-macosx_12_0_arm64.whl.metadata (3.3 kB)
Collecting dill<0.3.9,>=0.3.0 (from datasets>=2.19.1->mlx-vlm==0.1.25)
  Using cached dill-0.3.8-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: pandas in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from datasets>=2.19.1->mlx-vlm==0.1.25) (2.2.3)
Collecting xxhash (from datasets>=2.19.1->mlx-vlm==0.1.25)
  Downloading xxhash-3.5.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets>=2.19.1->mlx-vlm==0.1.25)
  Downloading multiprocess-0.70.16-py311-none-any.whl.metadata (7.2 kB)
Collecting fsspec<=2025.3.0,>=2023.1.0 (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25)
  Using cached fsspec-2025.3.0-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: huggingface-hub>=0.24.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from datasets>=2.19.1->mlx-vlm==0.1.25) (0.32.0)
Requirement already satisfied: packaging in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from datasets>=2.19.1->mlx-vlm==0.1.25) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from datasets>=2.19.1->mlx-vlm==0.1.25) (6.0.2)
Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (3.11.18)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (1.6.0)
Requirement already satisfied: multidict<7.0,>=4.5 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (6.4.4)
Requirement already satisfied: propcache>=0.2.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (0.3.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (1.20.0)
Requirement already satisfied: idna>=2.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from yarl<2.0,>=1.17.0->aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets>=2.19.1->mlx-vlm==0.1.25) (3.10)
Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from fastapi>=0.95.1->mlx-vlm==0.1.25) (0.46.2)
Requirement already satisfied: pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from fastapi>=0.95.1->mlx-vlm==0.1.25) (2.11.5)
Requirement already satisfied: typing-extensions>=4.8.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from fastapi>=0.95.1->mlx-vlm==0.1.25) (4.13.2)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi>=0.95.1->mlx-vlm==0.1.25) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi>=0.95.1->mlx-vlm==0.1.25) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi>=0.95.1->mlx-vlm==0.1.25) (0.4.1)
Requirement already satisfied: anyio<5,>=3.6.2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from starlette<0.47.0,>=0.40.0->fastapi>=0.95.1->mlx-vlm==0.1.25) (4.9.0)
Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi>=0.95.1->mlx-vlm==0.1.25) (1.3.1)
Requirement already satisfied: aiofiles<25.0,>=22.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (24.1.0)
Requirement already satisfied: ffmpy in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.5.0)
Requirement already satisfied: gradio-client==1.10.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (1.10.1)
Requirement already satisfied: groovy~=0.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.1.2)
Requirement already satisfied: httpx>=0.24.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.28.1)
Requirement already satisfied: jinja2<4.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (3.1.6)
Requirement already satisfied: markupsafe<4.0,>=2.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (3.0.2)
Requirement already satisfied: orjson~=3.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (3.10.18)
Requirement already satisfied: pydub in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.25.1)
Requirement already satisfied: python-multipart>=0.0.18 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.0.20)
Requirement already satisfied: ruff>=0.9.3 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.11.11)
Requirement already satisfied: safehttpx<0.2.0,>=0.1.6 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.1.6)
Requirement already satisfied: semantic-version~=2.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (2.10.0)
Requirement already satisfied: tomlkit<0.14.0,>=0.12.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.13.2)
Requirement already satisfied: typer<1.0,>=0.12 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.15.4)
Requirement already satisfied: uvicorn>=0.14.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio>=5.19.0->mlx-vlm==0.1.25) (0.34.2)
Requirement already satisfied: websockets<16.0,>=10.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from gradio-client==1.10.1->gradio>=5.19.0->mlx-vlm==0.1.25) (15.0.1)
Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from pandas->datasets>=2.19.1->mlx-vlm==0.1.25) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from pandas->datasets>=2.19.1->mlx-vlm==0.1.25) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from pandas->datasets>=2.19.1->mlx-vlm==0.1.25) (2025.2)
Requirement already satisfied: click<8.2,>=8.0.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from typer<1.0,>=0.12->gradio>=5.19.0->mlx-vlm==0.1.25) (8.1.8)
Requirement already satisfied: shellingham>=1.3.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from typer<1.0,>=0.12->gradio>=5.19.0->mlx-vlm==0.1.25) (1.5.4)
Requirement already satisfied: rich>=10.11.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from typer<1.0,>=0.12->gradio>=5.19.0->mlx-vlm==0.1.25) (13.9.4)
Requirement already satisfied: certifi in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from httpx>=0.24.1->gradio>=5.19.0->mlx-vlm==0.1.25) (2025.4.26)
Requirement already satisfied: httpcore==1.* in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from httpx>=0.24.1->gradio>=5.19.0->mlx-vlm==0.1.25) (1.0.9)
Requirement already satisfied: h11>=0.16 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.24.1->gradio>=5.19.0->mlx-vlm==0.1.25) (0.16.0)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from huggingface-hub>=0.24.0->datasets>=2.19.1->mlx-vlm==0.1.25) (1.1.2)
Collecting protobuf (from mlx-lm>=0.23.0->mlx-vlm==0.1.25)
  Using cached protobuf-6.31.0-cp39-abi3-macosx_10_9_universal2.whl.metadata (593 bytes)
Requirement already satisfied: six>=1.5 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas->datasets>=2.19.1->mlx-vlm==0.1.25) (1.17.0)
Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from requests>=2.31.0->mlx-vlm==0.1.25) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from requests>=2.31.0->mlx-vlm==0.1.25) (2.4.0)
Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio>=5.19.0->mlx-vlm==0.1.25) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio>=5.19.0->mlx-vlm==0.1.25) (2.19.1)
Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio>=5.19.0->mlx-vlm==0.1.25) (0.1.2)
Requirement already satisfied: regex!=2019.12.17 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from transformers>=4.51.3->mlx-vlm==0.1.25) (2024.11.6)
Requirement already satisfied: tokenizers<0.22,>=0.21 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from transformers>=4.51.3->mlx-vlm==0.1.25) (0.21.1)
Requirement already satisfied: safetensors>=0.4.3 in /Users/<USER>/.cua-venv/lib/python3.11/site-packages (from transformers>=4.51.3->mlx-vlm==0.1.25) (0.5.3)
Collecting sentencepiece!=0.1.92,>=0.1.91 (from transformers[sentencepiece]>=4.39.3->mlx-lm>=0.23.0->mlx-vlm==0.1.25)
  Downloading sentencepiece-0.2.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (7.7 kB)
Downloading opencv_python-*********-cp37-abi3-macosx_11_0_arm64.whl (54.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 54.8/54.8 MB 46.7 MB/s eta 0:00:00
Downloading datasets-3.6.0-py3-none-any.whl (491 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
Downloading fsspec-2025.3.0-py3-none-any.whl (193 kB)
Downloading multiprocess-0.70.16-py311-none-any.whl (143 kB)
Downloading mlx-0.25.2-cp311-cp311-macosx_15_0_arm64.whl (30.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 30.4/30.4 MB 48.1 MB/s eta 0:00:00
Downloading mlx_lm-0.24.1-py3-none-any.whl (194 kB)
Downloading pyarrow-20.0.0-cp311-cp311-macosx_12_0_arm64.whl (30.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 30.9/30.9 MB 56.4 MB/s eta 0:00:00
Downloading sentencepiece-0.2.0-cp311-cp311-macosx_11_0_arm64.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 43.6 MB/s eta 0:00:00
Using cached protobuf-6.31.0-cp39-abi3-macosx_10_9_universal2.whl (425 kB)
Downloading xxhash-3.5.0-cp311-cp311-macosx_11_0_arm64.whl (30 kB)
Building wheels for collected packages: mlx-vlm
  DEPRECATION: Building 'mlx-vlm' using the legacy setup.py bdist_wheel mechanism, which will be removed in a future version. pip 25.3 will enforce this behaviour change. A possible replacement is to use the standardized build interface by setting the `--use-pep517` option, (possibly combined with `--no-build-isolation`), or adding a `pyproject.toml` file to the source tree of 'mlx-vlm'. Discussion can be found at https://github.com/pypa/pip/issues/6334
  Building wheel for mlx-vlm (setup.py): started
  Building wheel for mlx-vlm (setup.py): finished with status 'done'
  Created wheel for mlx-vlm: filename=mlx_vlm-0.1.25-py3-none-any.whl size=251194 sha256=c291bd4905b24051d5469a42f0d63b815c613988fc804cba19985cec1dc1b213
  Stored in directory: /private/var/folders/1l/tn__n8td7bbf07qcrtzgh8p40000gn/T/pip-ephem-wheel-cache-ujc93y9e/wheels/33/67/dd/779b9f3f7a58289b81ee293d405d96d46f865df7aefa195e54
Successfully built mlx-vlm
Installing collected packages: sentencepiece, xxhash, pyarrow, protobuf, opencv-python, mlx, fsspec, dill, multiprocess, datasets, mlx-lm, mlx-vlm
  Attempting uninstall: opencv-python
    Found existing installation: opencv-python *********
    Uninstalling opencv-python-*********:
      Successfully uninstalled opencv-python-*********
  Attempting uninstall: fsspec
    Found existing installation: fsspec 2025.5.0
    Uninstalling fsspec-2025.5.0:
      Successfully uninstalled fsspec-2025.5.0

Successfully installed datasets-3.6.0 dill-0.3.8 fsspec-2025.3.0 mlx-0.25.2 mlx-lm-0.24.1 mlx-vlm-0.1.25 multiprocess-0.70.16 opencv-python-********* protobuf-6.31.0 pyarrow-20.0.0 sentencepiece-0.2.0 xxhash-3.5.0
✅ Setup complete!
🖥️  You can start the CUA playground by running: /Users/<USER>/.cua-demo/start_demo.sh
🔍 Checking if the macOS CUA VM is running...
🚀 Starting the macOS CUA VM in the background...
✅ VM started successfully.

Would you like to start the CUA playground now? (y/n) 
