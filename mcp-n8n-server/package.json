{"name": "mcp-n8n-server", "version": "1.0.0", "description": "Model Context Protocol server for n8n integration", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "test": "jest", "test-connection": "tsx src/test.ts"}, "keywords": ["mcp", "model-context-protocol", "n8n", "automation", "workflow"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.6.0", "dotenv": "^16.5.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}