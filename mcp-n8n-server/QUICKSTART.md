# Quick Start Guide - n8n MCP Server

## 🚀 Quick Setup (5 minutes)

### 1. Install and Build
```bash
cd mcp-n8n-server
npm install
npm run build
```

### 2. Configure Environment
```bash
cp .env.example .env
```

Edit `.env`:
```bash
N8N_BASE_URL=https://your-n8n-instance.com
N8N_API_KEY=your-api-key-here
```

### 3. Test Connection
```bash
npm run test-connection
```

### 4. Add to <PERSON>

Edit your Claude Desktop config file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "n8n": {
      "command": "node",
      "args": ["/full/path/to/mcp-n8n-server/dist/index.js"],
      "env": {
        "N8N_BASE_URL": "https://your-n8n-instance.com",
        "N8N_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

### 5. Restart Claude <PERSON>ktop

## 🧪 Test Commands

Try these commands in Claude:

```
List all my n8n workflows
```

```
Execute workflow "workflow-id-here" with data {"test": "value"}
```

```
Show me the execution history for the last 10 runs
```

```
Check the status of execution "execution-id-here"
```

## 🔧 Troubleshooting

### Connection Issues
```bash
# Test your connection
npm run test-connection

# Check if n8n is accessible
curl -H "X-N8N-API-KEY: your-key" https://your-n8n-instance.com/api/v1/workflows
```

### Permission Issues
- Ensure your API key has workflow access permissions
- Check that your n8n instance allows API access
- Verify the base URL is correct (no trailing slash)

### Claude Desktop Issues
- Restart Claude Desktop after config changes
- Check the full path to `dist/index.js` is correct
- Verify environment variables are set in the config

## 📚 Next Steps

- Read the full [README.md](README.md) for detailed documentation
- Check [examples/usage-examples.md](examples/usage-examples.md) for more use cases
- Review n8n API documentation for advanced features

## 🆘 Need Help?

1. Check the troubleshooting section above
2. Verify your n8n instance is accessible
3. Test the connection using `npm run test-connection`
4. Check Claude Desktop logs for error messages
