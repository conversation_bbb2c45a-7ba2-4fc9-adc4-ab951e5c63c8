/**
 * n8n API client for MCP server
 */
import axios from 'axios';
export class N8nClient {
    axios;
    config;
    constructor(config) {
        this.config = config;
        this.axios = axios.create({
            baseURL: `${config.baseUrl}/api/v1`,
            timeout: config.timeout || 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });
        // Setup authentication
        this.setupAuthentication();
        // Setup response interceptor for error handling
        this.setupErrorHandling();
    }
    setupAuthentication() {
        if (this.config.apiKey) {
            this.axios.defaults.headers.common['X-N8N-API-KEY'] = this.config.apiKey;
        }
        else if (this.config.username && this.config.password) {
            const auth = Buffer.from(`${this.config.username}:${this.config.password}`).toString('base64');
            this.axios.defaults.headers.common['Authorization'] = `Basic ${auth}`;
        }
    }
    setupErrorHandling() {
        this.axios.interceptors.response.use((response) => response, (error) => {
            const n8nError = {
                message: error.message,
                httpStatusCode: error.response?.status,
            };
            if (error.response?.data) {
                const errorData = error.response.data;
                n8nError.message = errorData.message || error.message;
                n8nError.code = errorData.code;
            }
            throw n8nError;
        });
    }
    /**
     * Get list of workflows
     */
    async getWorkflows() {
        try {
            const response = await this.axios.get('/workflows');
            return response.data.data;
        }
        catch (error) {
            throw this.handleError(error, 'Failed to fetch workflows');
        }
    }
    /**
     * Get workflow by ID
     */
    async getWorkflow(id) {
        try {
            const response = await this.axios.get(`/workflows/${id}`);
            return response.data.data;
        }
        catch (error) {
            throw this.handleError(error, `Failed to fetch workflow ${id}`);
        }
    }
    /**
     * Execute workflow
     */
    async executeWorkflow(request) {
        try {
            const payload = {};
            if (request.data) {
                payload.data = request.data;
            }
            if (request.waitTill) {
                payload.waitTill = request.waitTill.toISOString();
            }
            const response = await this.axios.post(`/workflows/${request.workflowId}/execute`, payload);
            return response.data.data;
        }
        catch (error) {
            throw this.handleError(error, `Failed to execute workflow ${request.workflowId}`);
        }
    }
    /**
     * Get execution by ID
     */
    async getExecution(id, includeData = false) {
        try {
            const params = includeData ? { includeData: 'true' } : {};
            const response = await this.axios.get(`/executions/${id}`, { params });
            return response.data.data;
        }
        catch (error) {
            throw this.handleError(error, `Failed to fetch execution ${id}`);
        }
    }
    /**
     * Get executions with filters
     */
    async getExecutions(filters = {}) {
        try {
            const params = {};
            if (filters.workflowId)
                params.workflowId = filters.workflowId;
            if (filters.status)
                params.status = filters.status;
            if (filters.limit)
                params.limit = filters.limit.toString();
            if (filters.cursor)
                params.cursor = filters.cursor;
            if (filters.includeData)
                params.includeData = 'true';
            const response = await this.axios.get('/executions', { params });
            return response.data.data;
        }
        catch (error) {
            throw this.handleError(error, 'Failed to fetch executions');
        }
    }
    /**
     * Get credentials (if user has access)
     */
    async getCredentials() {
        try {
            const response = await this.axios.get('/credentials');
            return response.data.data;
        }
        catch (error) {
            throw this.handleError(error, 'Failed to fetch credentials');
        }
    }
    /**
     * Test connection to n8n
     */
    async testConnection() {
        try {
            await this.axios.get('/workflows?limit=1');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    handleError(error, message) {
        if (error.httpStatusCode) {
            // Already processed by interceptor
            return error;
        }
        return {
            message: `${message}: ${error.message || 'Unknown error'}`,
            stack: error.stack,
        };
    }
}
//# sourceMappingURL=n8n-client.js.map