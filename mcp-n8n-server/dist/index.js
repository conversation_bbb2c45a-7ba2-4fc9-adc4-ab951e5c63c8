/**
 * MCP Server for n8n Integration
 *
 * This server provides Model Context Protocol integration with n8n,
 * allowing AI models to interact with n8n workflows and executions.
 */
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { N8nClient } from './n8n-client.js';
import { getConfig } from './config.js';
// Initialize configuration and n8n client
let n8nClient;
try {
    const config = getConfig();
    n8nClient = new N8nClient(config);
    console.error('✅ n8n MCP Server initialized successfully');
}
catch (error) {
    console.error('❌ Failed to initialize n8n MCP Server:', error);
    process.exit(1);
}
// Create MCP server
const server = new McpServer({
    name: "n8n-mcp-server",
    version: "1.0.0"
});
/**
 * Tool: List Workflows
 * Get all available workflows from n8n
 */
server.registerTool("list-workflows", {
    title: "List n8n Workflows",
    description: "Retrieve all workflows from n8n instance",
    inputSchema: {}
}, async () => {
    try {
        const workflows = await n8nClient.getWorkflows();
        const workflowList = workflows.map(workflow => ({
            id: workflow.id,
            name: workflow.name,
            active: workflow.active,
            createdAt: workflow.createdAt,
            updatedAt: workflow.updatedAt,
            tags: workflow.tags?.map(tag => tag.name) || []
        }));
        return {
            content: [{
                    type: "text",
                    text: `Found ${workflows.length} workflows:\n\n${JSON.stringify(workflowList, null, 2)}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `Error fetching workflows: ${error.message}`
                }],
            isError: true
        };
    }
});
/**
 * Tool: Get Workflow Details
 * Get detailed information about a specific workflow
 */
server.registerTool("get-workflow-details", {
    title: "Get Workflow Details",
    description: "Get detailed information about a specific workflow including nodes and connections",
    inputSchema: {
        workflowId: z.string().describe("The ID of the workflow to retrieve")
    }
}, async ({ workflowId }) => {
    try {
        const workflow = await n8nClient.getWorkflow(workflowId);
        const workflowDetails = {
            id: workflow.id,
            name: workflow.name,
            active: workflow.active,
            createdAt: workflow.createdAt,
            updatedAt: workflow.updatedAt,
            tags: workflow.tags?.map(tag => tag.name) || [],
            nodeCount: workflow.nodes.length,
            nodes: workflow.nodes.map(node => ({
                id: node.id,
                name: node.name,
                type: node.type,
                position: node.position
            })),
            settings: workflow.settings || {}
        };
        return {
            content: [{
                    type: "text",
                    text: `Workflow Details:\n\n${JSON.stringify(workflowDetails, null, 2)}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `Error fetching workflow details: ${error.message}`
                }],
            isError: true
        };
    }
});
/**
 * Tool: Execute Workflow
 * Execute a workflow by ID with optional input data
 */
server.registerTool("execute-workflow", {
    title: "Execute n8n Workflow",
    description: "Execute a workflow by ID with optional input data",
    inputSchema: {
        workflowId: z.string().describe("The ID of the workflow to execute"),
        data: z.record(z.any()).optional().describe("Optional input data for the workflow"),
        waitTill: z.string().optional().describe("ISO date string to schedule execution for later")
    }
}, async ({ workflowId, data, waitTill }) => {
    try {
        const request = {
            workflowId,
            data: data || undefined,
            waitTill: waitTill ? new Date(waitTill) : undefined
        };
        const execution = await n8nClient.executeWorkflow(request);
        const executionInfo = {
            id: execution.id,
            workflowId: execution.workflowId,
            status: execution.status,
            mode: execution.mode,
            startedAt: execution.startedAt,
            finished: execution.finished
        };
        return {
            content: [{
                    type: "text",
                    text: `Workflow execution started:\n\n${JSON.stringify(executionInfo, null, 2)}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `Error executing workflow: ${error.message}`
                }],
            isError: true
        };
    }
});
/**
 * Tool: Get Execution Status
 * Get the status and details of a specific execution
 */
server.registerTool("get-execution-status", {
    title: "Get Execution Status",
    description: "Get the status and details of a specific workflow execution",
    inputSchema: {
        executionId: z.string().describe("The ID of the execution to check"),
        includeData: z.boolean().optional().default(false).describe("Whether to include execution data")
    }
}, async ({ executionId, includeData }) => {
    try {
        const execution = await n8nClient.getExecution(executionId, includeData);
        const executionStatus = {
            id: execution.id,
            workflowId: execution.workflowId,
            status: execution.status,
            mode: execution.mode,
            startedAt: execution.startedAt,
            stoppedAt: execution.stoppedAt,
            finished: execution.finished,
            error: execution.data?.resultData?.error?.message || null,
            lastNodeExecuted: execution.data?.resultData?.lastNodeExecuted || null
        };
        return {
            content: [{
                    type: "text",
                    text: `Execution Status:\n\n${JSON.stringify(executionStatus, null, 2)}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `Error fetching execution status: ${error.message}`
                }],
            isError: true
        };
    }
});
/**
 * Tool: Get Executions History
 * Get execution history with optional filters
 */
server.registerTool("get-executions", {
    title: "Get Executions History",
    description: "Get execution history with optional filters for workflow ID, status, and limit",
    inputSchema: {
        workflowId: z.string().optional().describe("Filter by specific workflow ID"),
        status: z.enum(['new', 'running', 'success', 'error', 'canceled', 'crashed', 'waiting']).optional().describe("Filter by execution status"),
        limit: z.number().min(1).max(100).optional().default(20).describe("Maximum number of executions to return"),
        includeData: z.boolean().optional().default(false).describe("Whether to include execution data")
    }
}, async ({ workflowId, status, limit, includeData }) => {
    try {
        const filters = {
            workflowId: workflowId || undefined,
            status: status || undefined,
            limit: limit || undefined,
            includeData: includeData || undefined
        };
        const executions = await n8nClient.getExecutions(filters);
        const executionsList = executions.map(execution => ({
            id: execution.id,
            workflowId: execution.workflowId,
            status: execution.status,
            mode: execution.mode,
            startedAt: execution.startedAt,
            stoppedAt: execution.stoppedAt,
            finished: execution.finished
        }));
        return {
            content: [{
                    type: "text",
                    text: `Found ${executions.length} executions:\n\n${JSON.stringify(executionsList, null, 2)}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `Error fetching executions: ${error.message}`
                }],
            isError: true
        };
    }
});
/**
 * Tool: Test Connection
 * Test connection to n8n instance
 */
server.registerTool("test-connection", {
    title: "Test n8n Connection",
    description: "Test the connection to the n8n instance",
    inputSchema: {}
}, async () => {
    try {
        const isConnected = await n8nClient.testConnection();
        return {
            content: [{
                    type: "text",
                    text: isConnected
                        ? "✅ Connection to n8n is successful"
                        : "❌ Failed to connect to n8n"
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `❌ Connection test failed: ${error.message}`
                }],
            isError: true
        };
    }
});
/**
 * Tool: Get Credentials
 * Get available credentials (if user has access)
 */
server.registerTool("get-credentials", {
    title: "Get n8n Credentials",
    description: "Get list of available credentials (requires appropriate permissions)",
    inputSchema: {}
}, async () => {
    try {
        const credentials = await n8nClient.getCredentials();
        const credentialsList = credentials.map(credential => ({
            id: credential.id,
            name: credential.name,
            type: credential.type,
            nodesAccess: credential.nodesAccess.map(access => access.nodeType)
        }));
        return {
            content: [{
                    type: "text",
                    text: `Found ${credentials.length} credentials:\n\n${JSON.stringify(credentialsList, null, 2)}`
                }]
        };
    }
    catch (error) {
        return {
            content: [{
                    type: "text",
                    text: `Error fetching credentials: ${error.message}`
                }],
            isError: true
        };
    }
});
// ===== RESOURCES =====
/**
 * Resource: Workflows List
 * Provides access to all workflows as a resource
 */
server.registerResource("workflows", "n8n://workflows", {
    title: "n8n Workflows",
    description: "List of all workflows in n8n",
    mimeType: "application/json"
}, async (uri) => {
    try {
        const workflows = await n8nClient.getWorkflows();
        const workflowsData = workflows.map(workflow => ({
            id: workflow.id,
            name: workflow.name,
            active: workflow.active,
            createdAt: workflow.createdAt,
            updatedAt: workflow.updatedAt,
            tags: workflow.tags?.map(tag => tag.name) || [],
            nodeCount: workflow.nodes.length
        }));
        return {
            contents: [{
                    uri: uri.href,
                    text: JSON.stringify(workflowsData, null, 2),
                    mimeType: "application/json"
                }]
        };
    }
    catch (error) {
        return {
            contents: [{
                    uri: uri.href,
                    text: JSON.stringify({ error: error.message }, null, 2),
                    mimeType: "application/json"
                }]
        };
    }
});
/**
 * Resource: Executions History
 * Provides access to execution history as a resource
 */
server.registerResource("executions", "n8n://executions", {
    title: "n8n Executions",
    description: "Recent execution history from n8n",
    mimeType: "application/json"
}, async (uri) => {
    try {
        const executions = await n8nClient.getExecutions({ limit: 50 });
        const executionsData = executions.map(execution => ({
            id: execution.id,
            workflowId: execution.workflowId,
            status: execution.status,
            mode: execution.mode,
            startedAt: execution.startedAt,
            stoppedAt: execution.stoppedAt,
            finished: execution.finished
        }));
        return {
            contents: [{
                    uri: uri.href,
                    text: JSON.stringify(executionsData, null, 2),
                    mimeType: "application/json"
                }]
        };
    }
    catch (error) {
        return {
            contents: [{
                    uri: uri.href,
                    text: JSON.stringify({ error: error.message }, null, 2),
                    mimeType: "application/json"
                }]
        };
    }
});
// ===== SERVER STARTUP =====
/**
 * Start the MCP server
 */
async function main() {
    try {
        // Test connection on startup
        const isConnected = await n8nClient.testConnection();
        if (!isConnected) {
            console.error('❌ Failed to connect to n8n on startup');
            process.exit(1);
        }
        console.error('🔗 Connected to n8n successfully');
        // Connect to stdio transport
        const transport = new StdioServerTransport();
        await server.connect(transport);
        console.error('🚀 n8n MCP Server is running');
    }
    catch (error) {
        console.error('❌ Failed to start n8n MCP Server:', error);
        process.exit(1);
    }
}
// Handle graceful shutdown
process.on('SIGINT', () => {
    console.error('🛑 Shutting down n8n MCP Server...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.error('🛑 Shutting down n8n MCP Server...');
    process.exit(0);
});
// Start the server
main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map