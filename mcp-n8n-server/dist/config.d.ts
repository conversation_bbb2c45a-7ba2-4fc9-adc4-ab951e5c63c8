/**
 * Configuration management for n8n MCP server
 */
import { N8nConfig } from './types.js';
/**
 * Load configuration from environment variables
 */
export declare function loadConfig(): N8nConfig;
/**
 * Validate configuration
 */
export declare function validateConfig(config: N8nConfig): void;
/**
 * Get configuration with validation
 */
export declare function getConfig(): N8nConfig;
//# sourceMappingURL=config.d.ts.map