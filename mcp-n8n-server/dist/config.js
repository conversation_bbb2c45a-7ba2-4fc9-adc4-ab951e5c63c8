/**
 * Configuration management for n8n MCP server
 */
/**
 * Default configuration values
 */
const DEFAULT_CONFIG = {
    timeout: 30000, // 30 seconds
};
/**
 * Load configuration from environment variables
 */
export function loadConfig() {
    const baseUrl = process.env['N8N_BASE_URL'];
    if (!baseUrl) {
        throw new Error('N8N_BASE_URL environment variable is required');
    }
    // Validate base URL format
    try {
        new URL(baseUrl);
    }
    catch (error) {
        throw new Error(`Invalid N8N_BASE_URL format: ${baseUrl}`);
    }
    const config = {
        ...DEFAULT_CONFIG,
        baseUrl: baseUrl.replace(/\/$/, ''), // Remove trailing slash
    };
    // Authentication configuration
    const apiKey = process.env['N8N_API_KEY'];
    const username = process.env['N8N_USERNAME'];
    const password = process.env['N8N_PASSWORD'];
    if (apiKey) {
        config.apiKey = apiKey;
    }
    else if (username && password) {
        config.username = username;
        config.password = password;
    }
    else {
        throw new Error('Authentication required: Set either N8N_API_KEY or both N8N_USERNAME and N8N_PASSWORD');
    }
    // Optional timeout configuration
    const timeoutStr = process.env['N8N_TIMEOUT'];
    if (timeoutStr) {
        const timeout = parseInt(timeoutStr, 10);
        if (isNaN(timeout) || timeout <= 0) {
            throw new Error('N8N_TIMEOUT must be a positive number');
        }
        config.timeout = timeout;
    }
    return config;
}
/**
 * Validate configuration
 */
export function validateConfig(config) {
    if (!config.baseUrl) {
        throw new Error('Base URL is required');
    }
    if (!config.apiKey && (!config.username || !config.password)) {
        throw new Error('Authentication credentials are required');
    }
    if (config.timeout && config.timeout <= 0) {
        throw new Error('Timeout must be a positive number');
    }
}
/**
 * Get configuration with validation
 */
export function getConfig() {
    const config = loadConfig();
    validateConfig(config);
    return config;
}
//# sourceMappingURL=config.js.map