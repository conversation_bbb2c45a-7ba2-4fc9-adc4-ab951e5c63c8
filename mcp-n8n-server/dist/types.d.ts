/**
 * TypeScript types for n8n API integration
 */
export interface N8nConfig {
    baseUrl: string;
    apiKey?: string;
    username?: string;
    password?: string;
    timeout?: number;
}
export interface N8nWorkflow {
    id: string;
    name: string;
    active: boolean;
    createdAt: string;
    updatedAt: string;
    tags?: Array<{
        id: string;
        name: string;
    }>;
    nodes: N8nNode[];
    connections: Record<string, any>;
    settings?: Record<string, any>;
}
export interface N8nNode {
    id: string;
    name: string;
    type: string;
    typeVersion: number;
    position: [number, number];
    parameters: Record<string, any>;
}
export interface N8nExecution {
    id: string;
    workflowId: string;
    mode: 'manual' | 'trigger' | 'webhook' | 'retry';
    status: 'new' | 'running' | 'success' | 'error' | 'canceled' | 'crashed' | 'waiting';
    startedAt: string;
    stoppedAt?: string;
    finished: boolean;
    retryOf?: string;
    retrySuccessId?: string;
    data?: {
        resultData?: {
            runData?: Record<string, any>;
            lastNodeExecuted?: string;
            error?: {
                message: string;
                stack?: string;
            };
        };
    };
}
export interface N8nExecutionSummary {
    id: string;
    workflowId: string;
    mode: string;
    status: string;
    startedAt: string;
    stoppedAt?: string;
    finished: boolean;
}
export interface N8nCredential {
    id: string;
    name: string;
    type: string;
    nodesAccess: Array<{
        nodeType: string;
    }>;
    sharedWith?: Array<{
        id: string;
        email: string;
    }>;
}
export interface N8nApiResponse<T> {
    data: T;
}
export interface N8nListResponse<T> {
    data: T[];
    nextCursor?: string;
}
export interface N8nError {
    message: string;
    code?: string | undefined;
    httpStatusCode?: number | undefined;
    stack?: string | undefined;
}
export interface WorkflowExecuteRequest {
    workflowId: string;
    data?: Record<string, any> | undefined;
    waitTill?: Date | undefined;
}
export interface ExecutionFilters {
    workflowId?: string | undefined;
    status?: N8nExecution['status'] | undefined;
    limit?: number | undefined;
    cursor?: string | undefined;
    includeData?: boolean | undefined;
}
//# sourceMappingURL=types.d.ts.map