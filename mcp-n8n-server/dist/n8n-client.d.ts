/**
 * n8n API client for MCP server
 */
import { N8nConfig, N8nWorkflow, N8nExecution, N8nExecutionSummary, N8nCredential, WorkflowExecuteRequest, ExecutionFilters } from './types.js';
export declare class N8nClient {
    private readonly axios;
    private readonly config;
    constructor(config: N8nConfig);
    private setupAuthentication;
    private setupErrorHandling;
    /**
     * Get list of workflows
     */
    getWorkflows(): Promise<N8nWorkflow[]>;
    /**
     * Get workflow by ID
     */
    getWorkflow(id: string): Promise<N8nWorkflow>;
    /**
     * Execute workflow
     */
    executeWorkflow(request: WorkflowExecuteRequest): Promise<N8nExecution>;
    /**
     * Get execution by ID
     */
    getExecution(id: string, includeData?: boolean): Promise<N8nExecution>;
    /**
     * Get executions with filters
     */
    getExecutions(filters?: ExecutionFilters): Promise<N8nExecutionSummary[]>;
    /**
     * Get credentials (if user has access)
     */
    getCredentials(): Promise<N8nCredential[]>;
    /**
     * Test connection to n8n
     */
    testConnection(): Promise<boolean>;
    private handleError;
}
//# sourceMappingURL=n8n-client.d.ts.map