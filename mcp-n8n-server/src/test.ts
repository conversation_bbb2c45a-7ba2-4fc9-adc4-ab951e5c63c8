/**
 * Simple test script for n8n MCP Server
 * Run with: npm run test-connection
 */

import { N8nClient } from './n8n-client.js';
import { getConfig } from './config.js';

async function testN8nConnection() {
  try {
    console.log('🧪 Testing n8n MCP Server...');
    
    // Load configuration
    console.log('📋 Loading configuration...');
    const config = getConfig();
    console.log(`✅ Configuration loaded for: ${config.baseUrl}`);
    
    // Create client
    console.log('🔌 Creating n8n client...');
    const client = new N8nClient(config);
    
    // Test connection
    console.log('🔗 Testing connection...');
    const isConnected = await client.testConnection();
    
    if (!isConnected) {
      console.log('❌ Connection failed');
      return;
    }
    
    console.log('✅ Connection successful');
    
    // Test workflows
    console.log('📋 Fetching workflows...');
    const workflows = await client.getWorkflows();
    console.log(`✅ Found ${workflows.length} workflows`);
    
    if (workflows.length > 0) {
      const firstWorkflow = workflows[0];
      if (firstWorkflow) {
        console.log(`📄 First workflow: ${firstWorkflow.name} (${firstWorkflow.id})`);

        // Get workflow details
        console.log('🔍 Getting workflow details...');
        const details = await client.getWorkflow(firstWorkflow.id);
        console.log('📊 Workflow details:', JSON.stringify(details, null, 2));
        console.log(`✅ Workflow has ${details.nodes?.length || 0} nodes`);
      }
    }
    
    // Test executions
    console.log('📊 Fetching recent executions...');
    const executions = await client.getExecutions({ limit: 5 });
    console.log(`✅ Found ${executions.length} recent executions`);
    
    console.log('🎉 All tests passed!');
    
  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run tests
testN8nConnection();
