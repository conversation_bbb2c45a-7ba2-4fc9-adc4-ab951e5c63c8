/**
 * n8n API client for MCP server
 */

import axios, { AxiosInstance, AxiosError } from 'axios';
import {
  N8nConfig,
  N8nWorkflow,
  N8nExecution,
  N8nExecutionSummary,
  N8nCredential,
  N8nApiResponse,
  N8nListResponse,
  N8nError,
  WorkflowExecuteRequest,
  ExecutionFilters
} from './types.js';

export class N8nClient {
  private readonly axios: AxiosInstance;
  private readonly config: N8nConfig;

  constructor(config: N8nConfig) {
    this.config = config;
    
    this.axios = axios.create({
      baseURL: `${config.baseUrl}/api/v1`,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Setup authentication
    this.setupAuthentication();
    
    // Setup response interceptor for error handling
    this.setupErrorHandling();
  }

  private setupAuthentication(): void {
    if (this.config.apiKey) {
      this.axios.defaults.headers.common['X-N8N-API-KEY'] = this.config.apiKey;
    } else if (this.config.username && this.config.password) {
      const auth = Buffer.from(`${this.config.username}:${this.config.password}`).toString('base64');
      this.axios.defaults.headers.common['Authorization'] = `Basic ${auth}`;
    }
  }

  private setupErrorHandling(): void {
    this.axios.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        const n8nError: N8nError = {
          message: error.message,
          httpStatusCode: error.response?.status,
        };

        if (error.response?.data) {
          const errorData = error.response.data as any;
          n8nError.message = errorData.message || error.message;
          n8nError.code = errorData.code;
        }

        throw n8nError;
      }
    );
  }

  /**
   * Get list of workflows
   */
  async getWorkflows(): Promise<N8nWorkflow[]> {
    try {
      const response = await this.axios.get<N8nListResponse<N8nWorkflow>>('/workflows');
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch workflows');
    }
  }

  /**
   * Get workflow by ID
   */
  async getWorkflow(id: string): Promise<N8nWorkflow> {
    try {
      const response = await this.axios.get(`/workflows/${id}`);

      // n8n API returns data directly, not wrapped in {data: ...}
      return response.data;
    } catch (error) {
      throw this.handleError(error, `Failed to fetch workflow ${id}`);
    }
  }

  /**
   * Execute workflow
   */
  async executeWorkflow(request: WorkflowExecuteRequest): Promise<N8nExecution> {
    try {
      const payload: any = {};
      
      if (request.data) {
        payload.data = request.data;
      }
      
      if (request.waitTill) {
        payload.waitTill = request.waitTill.toISOString();
      }

      const response = await this.axios.post<N8nApiResponse<N8nExecution>>(
        `/workflows/${request.workflowId}/execute`,
        payload
      );
      
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, `Failed to execute workflow ${request.workflowId}`);
    }
  }

  /**
   * Get execution by ID
   */
  async getExecution(id: string, includeData: boolean = false): Promise<N8nExecution> {
    try {
      const params = includeData ? { includeData: 'true' } : {};
      const response = await this.axios.get<N8nApiResponse<N8nExecution>>(
        `/executions/${id}`,
        { params }
      );
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, `Failed to fetch execution ${id}`);
    }
  }

  /**
   * Get executions with filters
   */
  async getExecutions(filters: ExecutionFilters = {}): Promise<N8nExecutionSummary[]> {
    try {
      const params: any = {};
      
      if (filters.workflowId) params.workflowId = filters.workflowId;
      if (filters.status) params.status = filters.status;
      if (filters.limit) params.limit = filters.limit.toString();
      if (filters.cursor) params.cursor = filters.cursor;
      if (filters.includeData) params.includeData = 'true';

      const response = await this.axios.get<N8nListResponse<N8nExecutionSummary>>(
        '/executions',
        { params }
      );
      
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch executions');
    }
  }

  /**
   * Get credentials (if user has access)
   */
  async getCredentials(): Promise<N8nCredential[]> {
    try {
      const response = await this.axios.get<N8nListResponse<N8nCredential>>('/credentials');
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch credentials');
    }
  }

  /**
   * Test connection to n8n
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.axios.get('/workflows?limit=1');
      return true;
    } catch (error) {
      return false;
    }
  }

  private handleError(error: any, message: string): N8nError {
    if (error.httpStatusCode) {
      // Already processed by interceptor
      return error;
    }
    
    return {
      message: `${message}: ${error.message || 'Unknown error'}`,
      stack: error.stack,
    };
  }
}
