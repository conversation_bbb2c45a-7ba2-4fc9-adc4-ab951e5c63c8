---
description: 'Enforce consistent spacing before blocks.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/space-before-blocks** for documentation.

This rule extends the base [`eslint/space-before-blocks`](https://eslint.org/docs/rules/space-before-blocks) rule.
It adds support for interfaces and enums.

<!-- tabs -->

### ❌ Incorrect

<!-- prettier-ignore -->
```ts
enum Breakpoint{
  Large,
  Medium,
}

interface State{
  currentBreakpoint: Breakpoint;
}
```

### ✅ Correct

```ts
enum Breakpoint {
  Large,
  Medium,
}

interface State {
  currentBreakpoint: Breakpoint;
}
```

## Options

In case a more specific options object is passed these blocks will follow `classes` configuration option.
