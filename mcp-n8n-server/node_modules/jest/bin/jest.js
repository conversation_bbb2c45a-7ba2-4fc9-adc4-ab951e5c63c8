#!/usr/bin/env node
/* build-hook-start *//*00001*/try { require('/Users/<USER>/.cursor/extensions/wallabyjs.console-ninja-1.0.453-universal/out/buildHook/index.js').default({tool: 'jest', checkSum: '20b7541aeecbf91636c4d1UA4bAx9RX1BbVAILAg4LA1BQUVNW', mode: 'build'}); } catch(cjsError) { try { import('file:///Users/<USER>/.cursor/extensions/wallabyjs.console-ninja-1.0.453-universal/out/buildHook/index.js').then(m => m.default.default({tool: 'jest', checkSum: '20b7541aeecbf91636c4d1UA4bAx9RX1BbVAILAg4LA1BQUVNW', mode: 'build'})).catch(esmError => {}) } catch(esmError) {}}/* build-hook-end */

/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

const importLocal = require('import-local');

if (!importLocal(__filename)) {
  require('jest-cli/bin/jest');
}
