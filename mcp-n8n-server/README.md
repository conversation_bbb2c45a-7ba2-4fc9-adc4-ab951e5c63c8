# n8n MCP Server

Model Context Protocol (MCP) server for n8n integration. This server allows AI models to interact with n8n workflows, execute them, and monitor their status through the standardized MCP interface.

## Features

### Tools
- **list-workflows** - Get all available workflows from n8n
- **get-workflow-details** - Get detailed information about a specific workflow
- **execute-workflow** - Execute a workflow with optional input data
- **get-execution-status** - Get status and details of a specific execution
- **get-executions** - Get execution history with filters
- **test-connection** - Test connection to n8n instance
- **get-credentials** - Get available credentials (requires permissions)

### Resources
- **workflows** (`n8n://workflows`) - List of all workflows as a resource
- **executions** (`n8n://executions`) - Recent execution history as a resource

## Installation

1. Clone or download this repository
2. Navigate to the project directory:
```bash
cd mcp-n8n-server
```

3. Install dependencies:
```bash
npm install
```

4. Build the TypeScript code:
```bash
npm run build
```

5. Copy the environment configuration:
```bash
cp .env.example .env
```

6. Edit `.env` file with your n8n configuration:
```bash
# Required
N8N_BASE_URL=https://your-n8n-instance.com
N8N_API_KEY=your-api-key-here

# Optional
N8N_TIMEOUT=30000
```

## Configuration

The server requires environment variables for n8n connection:

### Required Variables

**n8n Base URL:**
```bash
export N8N_BASE_URL="https://your-n8n-instance.com"
```

**Authentication (choose one):**

Option 1 - API Key (recommended):
```bash
export N8N_API_KEY="your-api-key"
```

Option 2 - Basic Authentication:
```bash
export N8N_USERNAME="your-username"
export N8N_PASSWORD="your-password"
```

### Optional Variables

**Request Timeout:**
```bash
export N8N_TIMEOUT="30000"  # milliseconds, default: 30000
```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Using with MCP Client

Add to your MCP client configuration (e.g., Claude Desktop):

```json
{
  "mcpServers": {
    "n8n": {
      "command": "node",
      "args": ["/path/to/mcp-n8n-server/dist/index.js"],
      "env": {
        "N8N_BASE_URL": "https://your-n8n-instance.com",
        "N8N_API_KEY": "your-api-key"
      }
    }
  }
}
```

## Examples

### List All Workflows
```typescript
// Using the list-workflows tool
const workflows = await client.callTool({
  name: "list-workflows",
  arguments: {}
});
```

### Execute a Workflow
```typescript
// Execute workflow with input data
const execution = await client.callTool({
  name: "execute-workflow",
  arguments: {
    workflowId: "workflow-id-here",
    data: {
      "input": "some data"
    }
  }
});
```

### Check Execution Status
```typescript
// Get execution status
const status = await client.callTool({
  name: "get-execution-status",
  arguments: {
    executionId: "execution-id-here",
    includeData: true
  }
});
```

### Get Execution History
```typescript
// Get recent executions for a specific workflow
const executions = await client.callTool({
  name: "get-executions",
  arguments: {
    workflowId: "workflow-id-here",
    status: "success",
    limit: 10
  }
});
```

## API Reference

### Tools

#### list-workflows
- **Description**: Get all available workflows from n8n
- **Parameters**: None
- **Returns**: Array of workflow objects with basic information

#### get-workflow-details
- **Description**: Get detailed information about a specific workflow
- **Parameters**:
  - `workflowId` (string): The ID of the workflow to retrieve
- **Returns**: Detailed workflow object including nodes and settings

#### execute-workflow
- **Description**: Execute a workflow by ID with optional input data
- **Parameters**:
  - `workflowId` (string): The ID of the workflow to execute
  - `data` (object, optional): Input data for the workflow
  - `waitTill` (string, optional): ISO date string to schedule execution
- **Returns**: Execution object with status information

#### get-execution-status
- **Description**: Get status and details of a specific execution
- **Parameters**:
  - `executionId` (string): The ID of the execution to check
  - `includeData` (boolean, optional): Whether to include execution data
- **Returns**: Execution status object

#### get-executions
- **Description**: Get execution history with optional filters
- **Parameters**:
  - `workflowId` (string, optional): Filter by specific workflow ID
  - `status` (string, optional): Filter by execution status
  - `limit` (number, optional): Maximum number of executions (1-100, default: 20)
  - `includeData` (boolean, optional): Whether to include execution data
- **Returns**: Array of execution summary objects

#### test-connection
- **Description**: Test connection to n8n instance
- **Parameters**: None
- **Returns**: Connection status message

#### get-credentials
- **Description**: Get available credentials (requires appropriate permissions)
- **Parameters**: None
- **Returns**: Array of credential objects

### Resources

#### workflows (`n8n://workflows`)
- **Description**: List of all workflows in n8n
- **Format**: JSON array of workflow objects
- **MIME Type**: application/json

#### executions (`n8n://executions`)
- **Description**: Recent execution history from n8n
- **Format**: JSON array of execution objects (last 50)
- **MIME Type**: application/json

## Error Handling

The server includes comprehensive error handling:

- **Connection errors**: Automatic retry and clear error messages
- **Authentication errors**: Detailed feedback on credential issues
- **API errors**: n8n API error messages are passed through
- **Validation errors**: Input validation with helpful error messages

## Security Considerations

- Store API keys and credentials securely
- Use environment variables for sensitive configuration
- Consider using API keys instead of username/password for better security
- Ensure your n8n instance is properly secured and accessible
- Review n8n permissions for the user/API key being used

## Troubleshooting

### Connection Issues
1. Verify `N8N_BASE_URL` is correct and accessible
2. Check authentication credentials
3. Ensure n8n API is enabled and accessible
4. Test connection using the `test-connection` tool

### Permission Issues
1. Verify API key has necessary permissions
2. Check user permissions for workflow access
3. Some features (like credentials) require admin permissions

### Execution Issues
1. Verify workflow exists and is active
2. Check workflow configuration and required inputs
3. Monitor execution logs in n8n interface

## Development

### Building
```bash
npm run build
```

### Linting
```bash
npm run lint
```

### Testing
```bash
npm test
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review n8n API documentation
3. Open an issue in the repository
