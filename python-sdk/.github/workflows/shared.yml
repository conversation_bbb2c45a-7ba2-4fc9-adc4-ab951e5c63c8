name: Shared Checks

on:
  workflow_call:

jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Install the project
        run: uv sync --frozen --all-extras --dev --python 3.12

      - name: Run ruff format check
        run: uv run --no-sync ruff check .

  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Install the project
        run: uv sync --frozen --all-extras --dev --python 3.12

      - name: Run pyright
        run: uv run --no-sync pyright

  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12", "3.13"]
        os: [ubuntu-latest, windows-latest]

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Install the project
        run: uv sync --frozen --all-extras --dev --python ${{ matrix.python-version }}

      - name: Run pytest
        run: uv run --no-sync pytest
    continue-on-error: true
